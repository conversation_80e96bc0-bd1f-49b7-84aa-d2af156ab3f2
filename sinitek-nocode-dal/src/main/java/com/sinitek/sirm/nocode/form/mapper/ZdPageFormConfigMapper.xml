<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.form.mapper.ZdPageFormConfigMapper">


    <select id="findIdleTableNames" resultType="java.lang.String">
        <include refid="idleTableNameSql"/>
    </select>
    <select id="getIdleTableName" resultType="java.lang.String">
        <include refid="idleTableNameSql"/>
        limit 1
    </select>

    <sql id="idleTableNameSql">
        <bind name="countFlag" value="false"/>
        select tablename
        from (<include refid="findTable"/>) t
        order by t.sort
    </sql>

    <select id="getIdleTableLength" resultType="java.lang.Integer">
        <bind name="countFlag" value="true"/>
        <include refid="findTable"/>
    </select>
    <select id="getTableNameByFormCode" resultType="java.lang.String">
        select table_name
        from zd_page_form_config
        where form_code = #{formCode}
    </select>

    <sql id="findTable">
        select
        <choose>
            <when test="countFlag!=null and countFlag">
                count(1)
            </when>
            <otherwise>
                tablename, SUBSTRING(tablename FROM 19)::INTEGER as sort
            </otherwise>
        </choose>
        from pg_tables p
        where schemaname = current_schema()
        and tablename like 'zd_page_form_data%'
        and not exists(select 1 from zd_page_form_config f where f.table_name = p.tablename)
    </sql>
</mapper>
