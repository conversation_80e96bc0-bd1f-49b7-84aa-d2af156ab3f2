package com.sinitek.sirm.nocode.app.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0827
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdAppSearchParamPO extends PageDataParam {
    @ApiModelProperty(value = "名称", required = false, example = "报销系统")
    private String name;


    @ApiModelProperty(value = "应用状态", example = "1")
    private StatusEnum status;

    @ApiModelProperty(value = "是不是我创建的", example = "true")
    private Boolean myCreate;

    /**
     * 不是由前台传递
     */
    @ApiModelProperty(value = "当前登陆人orgId", example = "9990091")
    private String orgId;

    @ApiModelProperty(value = "主键查询", example = "1914879030746812418")
    private Long id;

    @ApiModelProperty(value = "是否是平台管理员", example = "true")
    private Boolean platformAdmin;

}
