package com.sinitek.sirm.nocode.support.mybatis.handler;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeException;
import org.postgresql.util.PGobject;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;

/**
 * jsonb类型转换器
 * <ul>
 *  <li>为什么不直接使用{@link JacksonTypeHandler} 因为当使用 @param 注解时，获取不到其属性类型，例如 {@link com.baomidou.mybatisplus.core.mapper.BaseMapper#updateById(Object)} 这个就使用了 param注解，导致找不到确切的属性类型</li>
 *  <li>灵活使用，有几种用法：</li>
 *  <li>1.直接使用本类，可以支持jsonb 和 varchar 类型(这个在数据库里面是逗号相隔的，出来是 json。使用的时候必须注明jdbcType 不然按 jsonb 处理)</li>
 *  <li>2.基础改类，其泛型为返回的结果类型</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2025.0321
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class JsonbTypeHandler<T> extends JacksonTypeHandler {
    protected static ObjectMapper objectMapper = new ObjectMapper();

    private final JavaType javaType;

    public JsonbTypeHandler() {
        super(List.class);
        javaType = getSuperclassTypeParameter(getClass());
    }

    private JsonbTypeHandler(Class<?> type, Class<?>... generics) {
        super(type);
        if (Objects.nonNull(generics) && generics.length > 0) {
            javaType = objectMapper.getTypeFactory().constructParametricType(type, generics);
        } else {
            javaType = objectMapper.getTypeFactory().constructType(type);
        }

    }

    protected JavaType getSuperclassTypeParameter(Class<?> clazz) {
        Type genericSuperclass = clazz.getGenericSuperclass();
        if (Objects.equals(genericSuperclass, JacksonTypeHandler.class)) {
            return objectMapper.getTypeFactory().constructType(String.class);
        }
        if (genericSuperclass instanceof Class) {
            // try to climb up the hierarchy until meet something useful
            if (JsonbTypeHandler.class != genericSuperclass) {
                return getSuperclassTypeParameter(clazz.getSuperclass());
            }

            throw new TypeException("'" + getClass() + "' extends TypeReference but misses the type parameter. "
                    + "Remove the extension or add a type parameter to it.");
        }
        Type rawType = ((ParameterizedType) genericSuperclass).getActualTypeArguments()[0];
        return objectMapper.getTypeFactory().constructType(rawType);
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {

        String value = null;
        if (parameter instanceof String) {
            value = (String) parameter;
        } else if (Objects.nonNull(parameter)) {
            value = toJson(parameter);
        }
        if (Objects.equals(jdbcType, JdbcType.VARCHAR)) {
            value = removeFix(value);
            ps.setString(i, value);
        } else {
            PGobject pgObject = new PGobject();
            pgObject.setType("jsonb");
            pgObject.setValue(value);
            ps.setObject(i, pgObject);
        }
    }

    /**
     * 去除首尾的[]
     *
     * @param value json 值
     * @return 去除后的json值
     */
    private String removeFix(String value) {
        if (Objects.nonNull(value)) {
            if (value.startsWith("[")) {
                value = value.substring(1);
            }
            if (value.endsWith("]")) {
                value = value.substring(0, value.length() - 1);
            }
        }
        return value;
    }

    private String addFix(String value) {
        if (Objects.nonNull(value)) {
            Class<?> firstType = javaType.getRawClass();
            if (Objects.equals(List.class, firstType) && !value.startsWith("[")) {
                value = "[" + value + "]";
            }
        }
        return value;
    }

    @SuppressWarnings("unchecked")
    @Override
    public T parse(String json) {
        try {
            Class<?> firstType = javaType.getRawClass();
            if (Objects.equals(String.class, firstType)) {
                return (T) json;
            }
            return objectMapper.readValue(addFix(json), javaType);
        } catch (IOException e) {
            log.error("JSON转换异常:{}", e.getMessage(), e);
            throw new BussinessException(e.getMessage());
        }
    }


    /**
     * 转换为对象
     *
     * @param object 对象
     * @return 转换后的对象
     */
    @SuppressWarnings("unchecked")
    public T parseObject(Object object) {
        if (Objects.isNull(object)) {
            return null;
        }
        if (object instanceof String) {
            return parse((String) object);
        }
        Class<?> rawClass = javaType.getRawClass();
        // 有多少个类型
        int i = javaType.containedTypeCount();
        if (i == 0 && rawClass.isAssignableFrom(object.getClass())) {
            // 直接返回
            return (T) object;
        } else if (i == 1 && rawClass.isAssignableFrom(List.class) && (object instanceof List)) {
            List<Object> list = (List<Object>) object;
            if (list.isEmpty()) {
                return (T) list;
            }
            Object o = list.stream().filter(Objects::nonNull).findFirst().orElse(null);
            if (Objects.isNull(o)) {
                return (T) list;
            }
            Class<?> subRawClass = javaType.containedType(0).getRawClass();
            if (subRawClass.isAssignableFrom(o.getClass())) {
                return (T) list;
            }
        }
        return parse(toJson(object));
    }


    public static void setObjectMapper(ObjectMapper objectMapper) {
        Assert.notNull(objectMapper, "ObjectMapper should not be null");
        JsonbTypeHandler.objectMapper = objectMapper;
    }

    /**
     * 创建一个实例
     *
     * @param rawType 实例类型
     * @param <T>     实例类型
     * @return 实例
     */
    public static <T> JsonbTypeHandler<T> ins(Class<?> rawType, Class<?>... generics) {
        return new JsonbTypeHandler<>(rawType, generics);
    }

    /**
     * 创建一个实例
     *
     * @param rawType 泛型类型
     * @param <T>     实例类型
     * @return 泛型实例
     */
    public static <T> JsonbTypeHandler<T> ins(Class<?> rawType) {
        return new JsonbTypeHandler<>(rawType);
    }
}
