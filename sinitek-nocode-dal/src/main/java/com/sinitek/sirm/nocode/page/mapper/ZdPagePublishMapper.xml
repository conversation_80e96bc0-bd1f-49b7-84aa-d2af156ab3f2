<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.page.mapper.ZdPagePublishMapper">

    <resultMap id="BaseResultMap" type="com.sinitek.sirm.nocode.page.entity.ZdPagePublish">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="publishType" column="publish_type" jdbcType="SMALLINT"/>
        <result property="shareType" column="share_type" jdbcType="SMALLINT"/>
        <result property="attachmentId" column="attachment_id" jdbcType="BIGINT"/>
        <result property="pageCode" column="page_code" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createTimeStamp" column="createtimestamp" jdbcType="TIMESTAMP"/>
        <result property="updateTimeStamp" column="updatetimestamp" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, url, publish_type, share_type, attachment_id, page_code, 
        version, createtimestamp, updatetimestamp
    </sql>

</mapper>
