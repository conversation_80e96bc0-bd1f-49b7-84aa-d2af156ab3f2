package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import com.sinitek.sirm.nocode.page.constant.PageConstant;
import com.sinitek.sirm.nocode.page.enumerate.PagePublishShareTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PagePublishTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面发布实体类
 *
 * <AUTHOR>
 * @version 2025.0818
 * @TableName zd_page_publish
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = PageConstant.PUBLISH_TABLE_NAME)
@Data
public class ZdPagePublish extends BaseEntity {

    /**
     * 自定义的页面地址
     */
    @ApiModelProperty(value = "自定义的页面地址", example = "jicode123456", required = true)
    private String url;

    /**
     * 页面编码
     */
    @ApiModelProperty(value = "表单编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
    private String pageCode;

    /**
     * 页面发布类型
     */
    private PagePublishTypeEnum publishType;

    /**
     * 页面发布类型
     */
    private PagePublishShareTypeEnum shareType;

    /**
     * 附件id
     */
    @ApiModelProperty(value = "附件id", example = "100000")
    private Long attachmentId;
}
