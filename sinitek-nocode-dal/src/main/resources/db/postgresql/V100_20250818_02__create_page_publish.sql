create table if not exists zd_page_publish
(
    id              bigint       not null
        primary key,
    url             varchar(50)  not null,
    publish_type    smallint,
    share_type      smallint,
    attachment_id   bigint,
    page_code       varchar(50)  not null,
    version         integer      not null,
    createtimestamp timestamp(0) not null,
    updatetimestamp timestamp(0) not null
);
comment on table zd_page_publish is '页面发布表';
comment on column zd_page_publish.url is '页面发布地址';
comment on column zd_page_publish.publish_type is '页面发布类型';
comment on column zd_page_publish.share_type is '页面分享类型';
comment on column zd_page_publish.attachment_id is '页面发布图片附件id';
comment on column zd_page_publish.version is '乐观锁';
comment on column zd_page_publish.createtimestamp is '创建时间';
comment on column zd_page_publish.updatetimestamp is '更新时间';

create index if not exists in_zd_page_publish_page_code
    on zd_page_publish (page_code);


alter table zd_page
    drop column if exists url,
    drop column if exists publish_type,
    drop column if exists attachment_id,
    drop column if exists thread_id;



