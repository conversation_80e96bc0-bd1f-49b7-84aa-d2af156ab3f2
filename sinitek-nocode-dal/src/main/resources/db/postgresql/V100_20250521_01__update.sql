-- 批量执行
drop function if exists update_page_form_data_batch;
create function update_page_form_data_batch(alertSql text, hasReturn boolean) returns integer as
$$
DECLARE
    table_counter        INT ;
    table_table_name_sql TEXT;
    table_name           TEXT;
    update_sql           TEXT;
    row_data             RECORD; -- 定义记录变量
    r                    RECORD;
    error_message        TEXT; -- 存储错误信息
    error_detail         TEXT; -- 存储错误详情
    error_context        TEXT;
BEGIN
    table_table_name_sql := 'select tablename
from pg_tables
where schemaname = current_schema()
  and tablename like ''zd_page_form_data%''';
    table_counter := 0;
    for row_data in execute table_table_name_sql
        LOOP
            table_name := row_data.tablename;
            update_sql := replace(alertSql, '%s', table_name);
            begin
                -- 捕获异常
                if hasReturn then
                    execute update_sql into r;
                    RAISE NOTICE '当前值：% 表名：%', r,table_name;
                else
                    execute update_sql;
                    RAISE NOTICE '表名：%', table_name;
                end if;
                table_counter := table_counter + 1;
            EXCEPTION
                WHEN OTHERS THEN
                    -- 获取错误信息
                    GET STACKED DIAGNOSTICS
                        error_message = MESSAGE_TEXT,
                        error_detail = PG_EXCEPTION_DETAIL,
                        error_context = PG_EXCEPTION_CONTEXT;
                    RAISE NOTICE '错误信息: %', error_message;
                    RAISE NOTICE '错误详情: %', error_detail;
                    RAISE NOTICE '错误内容: %', error_context;
            END;
        end loop;
    RETURN table_counter; -- 返回创建的表数量
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Error creating tables: %', SQLERRM;
END;
$$ language plpgsql;