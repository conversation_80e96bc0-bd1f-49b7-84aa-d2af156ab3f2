package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.IdEncryptUtil;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.nocode.page.constant.PageConstant;
import com.sinitek.sirm.nocode.page.dao.ZdPagePublishDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPagePublishDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPagePublish;
import com.sinitek.sirm.nocode.page.enumerate.PagePublishTypeEnum;
import com.sinitek.sirm.nocode.page.mapper.ZdPagePublishMapper;
import com.sinitek.sirm.nocode.page.mapstruct.ZdPagePublishMapstruct;
import com.sinitek.sirm.nocode.page.service.IZdPagePublishService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 页面发布Service实现类
 *
 * <AUTHOR>
 * @version 2025.0818
 * @description 针对表【zd_page_publish(页面发布表)】的数据库操作Service实现
 * @since 1.0.0-SNAPSHOT
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ZdPagePublishServiceImpl extends BaseDAO<ZdPagePublishMapper, ZdPagePublish, ZdPagePublishDAO>
        implements IZdPagePublishService {

    private final ZdPagePublishMapstruct mapstruct;
    private final IAttachmentExtService attachmentExtService;


    @Override
    public boolean saveOrUpdate(ZdPagePublishDTO dto) {
        check(Collections.singletonList(dto));
        ZdPagePublish one = get(dto.getCode());
        if (Objects.isNull(one)) {
            one = new ZdPagePublish();
            one.setId(IdWorker.getId(one));
        }
        mapstruct.updateEntityFromDto(dto, one);
        Boolean publishToSquare = dto.getPublishToSquare();
        PagePublishTypeEnum publishType = null;
        if (BooleanUtils.isTrue(publishToSquare)) {
            publishType = PagePublishTypeEnum.SQUARE;
        }
        one.setPublishType(publishType);

        // 附件上传
        Long attachmentId = null;
        UploadDTO uploadDTO = dto.getUploadDTO();
        if (Objects.nonNull(uploadDTO)) {
            List<UploadFileDTO> uploadFileList = uploadDTO.getUploadFileList();
            List<UploadFileDTO> removeFileList = uploadDTO.getRemoveFileList();
            if (CollectionUtils.isNotEmpty(uploadFileList) || CollectionUtils.isNotEmpty(removeFileList)) {
                Long sourceId = one.getId();
                attachmentExtService.saveAvatar(uploadDTO, sourceId, PageConstant.PUBLISH_TABLE_NAME);
                AttachmentDTO attachment = attachmentExtService.getAttachment(PageConstant.PUBLISH_TABLE_NAME, sourceId, 0);
                if (Objects.nonNull(attachment)) {
                    String id = attachment.getId();
                    attachmentId = NumberTool.safeToLong(IdEncryptUtil.decrypt(id), null);
                }
            } else {
                attachmentId = one.getAttachmentId();
            }
        }
        one.setAttachmentId(attachmentId);
        return dao.saveOrUpdate(one);
    }

    @Override
    public ZdPagePublishDTO getByPageCode(String pageCode) {
        ZdPagePublish zdPagePublish = get(pageCode);
        if (Objects.isNull(zdPagePublish)) {
            zdPagePublish = new ZdPagePublish();
            zdPagePublish.setId(0L);
        }
        ZdPagePublishDTO dto = mapstruct.toDTO(zdPagePublish);
        dto.setPublishToSquare(Objects.equals(zdPagePublish.getPublishType(), PagePublishTypeEnum.SQUARE));
        return dto;
    }

    private ZdPagePublish get(String pageCode) {
        LambdaQueryWrapper<ZdPagePublish> queryWrapper = eqOrIn(ZdPagePublish::getPageCode, pageCode);
        return dao.getOne(queryWrapper);
    }


    private void check(List<ZdPagePublishDTO> pageCustomUrlList) {
        pageCustomUrlList.forEach(pageCustomUrlDTO -> {
            String url = pageCustomUrlDTO.getUrl();
            LamWrapper<ZdPagePublish> queryWrapper = LamWrapper.eqOrIn(ZdPagePublish::getUrl, url)
                    .select(ZdPagePublish::getPageCode);
            String newCode = stringValue(queryWrapper);
            if (StringUtils.isNotBlank(newCode) && !Objects.equals(pageCustomUrlDTO.getCode(), newCode)) {
                throw new BussinessException("3000018", url);
            }
        });
    }
}
