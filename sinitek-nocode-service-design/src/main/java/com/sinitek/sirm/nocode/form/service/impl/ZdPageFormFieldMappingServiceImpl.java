package com.sinitek.sirm.nocode.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormFieldMappingDAO;
import com.sinitek.sirm.nocode.form.dto.ZdComponentDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormPageDataDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormFieldMapping;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormFieldMappingMapper;
import com.sinitek.sirm.nocode.form.service.IZdPageFormFieldMappingService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 表单字段映射表Service实现类
 *
 * <AUTHOR>
 * @version 2025.0604
 * @description 针对表【zd_page_form_field_mapping(表单字段映射表)】的数据库操作Service实现
 * @since 1.0.0-SNAPSHOT
 */
@Service
@Slf4j
public class ZdPageFormFieldMappingServiceImpl extends BaseDAO<ZdPageFormFieldMappingMapper, ZdPageFormFieldMapping, ZdPageFormFieldMappingDAO>
        implements IZdPageFormFieldMappingService {

    @Override
    public String getFieldMappingCode(String formCode, String name) {
        ZdPageFormFieldMapping one = dao.getOne(query(name, formCode));
        if (Objects.nonNull(one)) {
            return one.getCode();
        }
        return null;
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, String> getFieldMappingCode(String formCode) {
        return dao.list(query(null, formCode)).stream().collect(Collectors.toMap(ZdPageFormFieldMapping::getName, ZdPageFormFieldMapping::getCode));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdate(String formCode, String pageData) {
        ZdFormPageDataDTO zdFormPageDataDTO = JsonUtil.toJavaObject(pageData, ZdFormPageDataDTO.class);
        List<ZdComponentDTO> pageDataDTOList = new ArrayList<>();
        String childFormValue = PageDataComponentTypeEnum.ZD_CHILD_FORM.getValue();
        String formValue = PageDataComponentTypeEnum.ZD_FORM.getValue();
        zdFormPageDataDTO.walk((a, b) -> {
            if (Objects.nonNull(a)) {
                String parentComponentName = a.getComponentName();
                String componentName = b.getComponentName();
                if (Objects.equals(parentComponentName, formValue) && !Objects.equals(componentName, childFormValue)) {
                    ZdComponentDTO zdComponentDTO = b.makeComponentDTO(parentComponentName);
                    String label = zdComponentDTO.getLabel();
                    if (StringUtils.isNotBlank(label)) {
                        pageDataDTOList.add(zdComponentDTO);
                    }
                }
            }
        });
        if (CollectionUtils.isNotEmpty(pageDataDTOList)) {
            List<String> names = pageDataDTOList.stream().map(ZdComponentDTO::getLabel).distinct().collect(Collectors.toList());
            // 先删除，再新增
            dao.remove(query(names, formCode));
            List<ZdPageFormFieldMapping> pageFormFieldMappingList = pageDataDTOList.stream().map(p -> {
                ZdPageFormFieldMapping zdPageFormFieldMapping = new ZdPageFormFieldMapping();
                zdPageFormFieldMapping.setFormCode(formCode);
                zdPageFormFieldMapping.setName(p.getLabel());
                zdPageFormFieldMapping.setCode(p.getRef());
                return zdPageFormFieldMapping;
            }).collect(Collectors.toList());
            dao.saveBatch(pageFormFieldMappingList);
        }
    }


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM_CONFIG + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> formCodeList = appDeleteEvent.getCodeList();
        if (CollectionUtils.isEmpty(formCodeList)) {
            return;
        }
        LambdaQueryWrapper<ZdPageFormFieldMapping> queryWrapper = eqOrIn(ZdPageFormFieldMapping::getFormCode, formCodeList);
        dao.remove(queryWrapper);
        log.info("字段映射配置删除成功！");
    }

    private LambdaQueryWrapper<ZdPageFormFieldMapping> query(Object names, String formCode) {
        return eqOrIn(ZdPageFormFieldMapping::getName, names)
                .eq(ZdPageFormFieldMapping::getFormCode, formCode);
    }
}
