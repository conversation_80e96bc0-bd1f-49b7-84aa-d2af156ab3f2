package com.sinitek.sirm.nocode.common.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.common.properties.ZdGlobalProperties;
import com.sinitek.sirm.nocode.common.utils.BaseEnumUtils;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/common", tags = "零代码通用接口")
@RequestMapping("/frontend/api/nocode/common")
public class ZdCommonController {
    /**
     * 枚举缓存
     */
    private static final Map<String, List<ZdOptionDTO<?>>> CACHE_MAP = new HashMap<>(8);

    @Autowired
    private ZdGlobalProperties zdGlobalProperties;

    @ApiOperation(value = "获取枚举", notes = "<h3>参数说明(modelName,enumName,type):</h3>" +
            "<ul>" +
            "<li>启用禁用枚举:(common,statusEnum)</li>" +
            "<li>是否枚举:(common,yesOrNoEnum)</li>" +
            "<li>平台类型枚举:(app,platformTypeEnum)</li>" +
            "<li>权限成员类型:(page,memberTypeEnum)</li>" +
            "<li>权限字段类型枚举:(page,fieldAuthFlagEnum)</li>" +
            "<li>数据范围枚举:(page,dataScopeEnum)</li>" +
            "<li>页面权限类型枚举:(page,pageAuthTypeEnum)</li>" +
            "<li>操作权限枚举（提交权限）:(page,operationAuthEnum,0)</li>" +
            "<li>操作权限枚举（数据权限）:(page,operationAuthEnum,1)</li>" +
            "<li>页面类型枚举:(page,pageTypeEnum)</li>" +
            "<li>场景类型枚举:(page,sceneTypeEnum)</li>" +
            "<li>场景重复类型枚举:(page,sceneRepeatTypeEnum)</li>" +
            "<li>场景提交规则枚举:(page,sceneSubmitEnum)</li>" +
            "<li>场景通知方式枚举:(page,noteTypeEnum)</li>" +
            "<li>条件关系枚举( 例如：or and):(form,logicOperatorEnum)</li>" +
            "<li>操作类型枚举(大于，小于，包含等):(form,operatorEnum)</li>" +
            "<li>变量类型(固定值，变量):(form,variableTypeEnum)</li>" +
            "<li>值的类型(例如：int,text 等):(form,valueTypeEnum)</li>" +
            "</ul>"
    )
    @SuppressWarnings("java:S1452")
    @GetMapping("/get-enumerate")
    public RequestResult<List<ZdOptionDTO<?>>> getEnumerate(
            @ApiParam(name = "modelName", value = "枚举模块名称", example = "page", required = true)
            @RequestParam("modelName") String modelName,
            @ApiParam(name = "enumName", value = "枚举名称", example = "memberTypeEnum", required = true)
            @RequestParam("enumName") String enumName,
            @ApiParam(name = "type", value = "枚举类型", example = "1")
            @RequestParam(value = "type", required = false, defaultValue = "0") int type
    ) {
        String key = modelName + "." + enumName + "." + type;
        if (CACHE_MAP.containsKey(key)) {
            return new RequestResult<>(CACHE_MAP.get(key));
        }
        List<ZdOptionDTO<?>> option = ConvertUtil.list(BaseEnumUtils.option(modelName, enumName, type));
        CACHE_MAP.put(key, option);
        return new RequestResult<>(option);
    }

    @GetMapping("/get-help-doc-url")
    public RequestResult<String> getHelpDocUrl() {
        String helpDocUrl = this.zdGlobalProperties.getHelpDocUrl();
        return new RequestResult<>(helpDocUrl);
    }
}
