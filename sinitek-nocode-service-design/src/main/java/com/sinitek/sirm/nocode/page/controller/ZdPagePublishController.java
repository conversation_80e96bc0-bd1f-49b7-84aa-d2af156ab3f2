package com.sinitek.sirm.nocode.page.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.page.dto.ZdPagePublishDTO;
import com.sinitek.sirm.nocode.page.service.IZdPagePublishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 页面发布Controller
 *
 * <AUTHOR>
 * @version 2025.0818
 * @description 针对表【zd_page_publish(页面发布表)】的控制器
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Api(tags = "页面发布管理", value = "/frontend/api/nocode/page/publish")
@RestController
@RequestMapping("frontend/api/nocode/page/publish")
@RequiredArgsConstructor
public class ZdPagePublishController {

    private final IZdPagePublishService zdPagePublishService;


    @ApiOperation(value = "获取页面发布")
    @GetMapping("/get")
    public RequestResult<ZdPagePublishDTO> getUrlByCode(
            @ApiParam(name = FormConstant.FORM_CODE, value = "页面编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
            @RequestParam(FormConstant.FORM_CODE) String pageCode
    ) {
        return new RequestResult<>(zdPagePublishService.getByPageCode(pageCode));
    }

    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "页面发布")
    @PostMapping("/save-or-update")
    public RequestResult<Boolean> saveOrUpdate(
            @ApiParam(name = "页面自定义urlDTOList", value = "是个数组，可以批量修改")
            @Valid
            @RequestBody ZdPagePublishDTO pageCustomUrl
    ) {
        return new RequestResult<>(zdPagePublishService.saveOrUpdate(pageCustomUrl));
    }

}
