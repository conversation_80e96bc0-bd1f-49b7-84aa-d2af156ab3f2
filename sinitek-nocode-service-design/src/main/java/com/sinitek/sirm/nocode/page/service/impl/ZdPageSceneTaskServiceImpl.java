package com.sinitek.sirm.nocode.page.service.impl;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.sirm.common.encryption.algorithm.asymmetric.IAsymmetricEncryption;
import com.sinitek.sirm.common.message.template.dto.MessageContextDTO;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.message.template.dto.MessageSendConfigDTO;
import com.sinitek.sirm.common.message.template.dto.MessageSendFailedReceiverDTO;
import com.sinitek.sirm.common.message.template.enumerate.SendModeTypeEnum;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.config.ThreadPoolConfig;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.config.ZdCommonConfig;
import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.common.properties.NocodeDomainProperties;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.common.utils.ZdDateUtil;
import com.sinitek.sirm.nocode.common.utils.ZdHttpUtil;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.message.support.ZdMessageTemplateExtService;
import com.sinitek.sirm.nocode.page.dao.ZdPageSceneDAO;
import com.sinitek.sirm.nocode.page.dao.ZdPageSceneTaskDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageShareDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import com.sinitek.sirm.nocode.page.entity.ZdPageSceneTask;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.NoteTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneRepeatTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.page.mapper.ZdPageSceneTaskMapper;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageSceneTaskService;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import com.sinitek.sirm.nocode.page.support.repeat.base.ZdRepeatCreator;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.routine.holiday.service.IHolidaysService;
import com.sinitek.sirm.user.service.IUserFlagTokenService;
import com.sinitek.sirm.wxwork.dto.WxWorkSpecificAttrDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【zd_page_scene_task(页面场景任务表)】的数据库操作Service实现
 * 负责处理定时任务调度、消息发送及工作日计算等核心业务逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZdPageSceneTaskServiceImpl extends BaseDAO<ZdPageSceneTaskMapper, ZdPageSceneTask, ZdPageSceneTaskDAO> implements IZdPageSceneTaskService {

    private static final long LOCK_WAIT_TIME = 0L;
    private static final long LOCK_LEASE_TIME = 30L;
    private static final String LOCK_KEY = "zd_page_scene_task_lock";


    private static final Integer CHECK_DATES = 10;


    private final ZdMessageTemplateExtService messageTemplateExtService;
    private final IHolidaysService holidaysService;
    private final NocodeDomainProperties nocodeDomainProperties;
    private final IZdPageAuthService authService;
    private final IZdPageService pageService;

    private final IUserFlagTokenService userFlagTokenService;
    private final ZdPageSceneDAO pageSceneDAO;
    private final RedissonClient redissonClient;
    private final IAsymmetricEncryption encryption;
    private final ZdCommonConfig zdCommonConfig;

    private LocalTime endTime;


    /**
     * 定时发送每日报告任务（每天18:00执行）
     *
     * <p>该方法执行以下主要步骤：</p>
     * <ol>
     *     <li>查询当日需发送的报告场景</li>
     *     <li>构造表单报告链接</li>
     *     <li>构建并发送消息通知</li>
     * </ol>
     *
     * @implNote 未配置接收人时记录日志并跳过发送
     * @see #sendMessage(ZdPageScene, LocalDate) 发送消息的具体实现
     */
    @Override
    public void report() {
        List<ZdPageScene> nowDayReport = pageSceneDAO.findNowDayReport();
        if (CollectionUtils.isEmpty(nowDayReport)) {
            return;
        }
        // 遍历所有需要发送报告的场景
        nowDayReport.forEach(this::report);
    }


    /**
     * 发送表单收集报告
     *
     * <p>该方法用于向指定的接收人发送表单收集报告，包含以下主要步骤：</p>
     * <ol>
     *     <li>验证并处理报告接收人列表</li>
     *     <li>构建消息上下文和发送方式</li>
     *     <li>设置消息接收人</li>
     *     <li>调用基础发送方法发送消息</li>
     * </ol>
     *
     * @param zdPageScene 页面场景对象，包含报告配置信息
     * @implNote 当未配置接收人时记录日志并跳过发送流程
     * @implNote 消息发送采用同步方式进行，确保发送的可靠性
     */
    private void report(ZdPageScene zdPageScene) {
        List<String> reportSendOrgIds = validReportSendOrgIds(zdPageScene);

        // 如果未配置接收人，记录日志并跳过
        if (CollectionUtils.isEmpty(reportSendOrgIds)) {
            log.info("没有配置报告发送人!场景主键：{}", zdPageScene.getId());
            return;
        }

        if (!shouldSendReport(zdPageScene)) {
            return;
        }

        List<Employee> employeeList = createEmployeeList(reportSendOrgIds);

        // 构建消息上下文
        MessageContextDTO messageContextDTO = buildReportMessageContext(zdPageScene);

        // 设置消息标题和内容
        messageContextDTO.setTitle("收集表单报告");
        sendMessageBase(zdPageScene, zdPageScene.getFinishDate(), employeeList, messageContextDTO, getReportUrlFunction());
    }

    /**
     * 获取有效的报告发送组织ID列表
     *
     * @param zdPageScene 页面场景对象
     * @return 有效的组织ID列表
     */
    private List<String> validReportSendOrgIds(ZdPageScene zdPageScene) {
        List<String> reportSendOrgIds = zdPageScene.getReportSendOrgIds();
        if (!CollectionUtils.isEmpty(reportSendOrgIds)) {
            reportSendOrgIds = reportSendOrgIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        return reportSendOrgIds;
    }

    /**
     * 判断是否应该发送报告
     *
     * @param zdPageScene 页面场景对象
     * @return 是否应该发送报告
     */
    private boolean shouldSendReport(ZdPageScene zdPageScene) {
        LocalDate now = LocalDate.now();
        SceneRepeatTypeEnum repeatType = zdPageScene.getRepeatType();
        if (Objects.equals(repeatType, SceneRepeatTypeEnum.WEEK)) {
            return shouldSendWeeklyReport(zdPageScene, now);
        } else if (Objects.equals(repeatType, SceneRepeatTypeEnum.MONTH)) {
            return shouldSendMonthlyReport(zdPageScene, now);
        }
        return true;
    }

    /**
     * 判断是否应该发送周报
     *
     * @param now 当前日期
     * @return 是否应该发送周报
     */
    private boolean shouldSendWeeklyReport(ZdPageScene zdPageScene, LocalDate now) {
        //LocalDate finishDate = zdPageScene.getFinishDate();
        //YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();
        Integer weekDay = ConvertUtil.nullDefault(zdPageScene.getWeekDay(), 1);
        int value = now.getDayOfWeek().getValue();
        int diff = weekDay - value;
        if (diff == 1 || diff == -6) {
            return true;
        }
        return false;

        // 下周
        //LocalDate nextWeekDay = finishDate.plusWeeks(1).minusDays(1);
        //return shouldSendMessageBasedOnDate(holidayStrategy, now, nextWeekDay);
    }

    /**
     * 判断是否应该发送月报
     *
     * @param now 当前日期
     * @return 是否应该发送月报
     */
    private boolean shouldSendMonthlyReport(ZdPageScene zdPageScene, LocalDate now) {
        //LocalDate finishDate = zdPageScene.getFinishDate();
        //YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();
        //LocalDate nextMonthDay = finishDate.plusMonths(1).minusDays(1);
        //return shouldSendMessageBasedOnDate(holidayStrategy, now, nextMonthDay);
        // 每个月的多少号
        Integer monthDay = ConvertUtil.nullDefault(zdPageScene.getMonthDay(), 1);
        int dayOfMonth = now.getDayOfMonth();
        if (monthDay == 1) {
            // 当月底
            return dayOfMonth == now.with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
        } else {
            // 前一天
            return (monthDay - dayOfMonth) == 1;
        }


    }

    /**
     * 根据组织ID列表创建员工列表
     *
     * @param reportSendOrgIds 组织ID列表
     * @return 员工列表
     */
    private List<Employee> createEmployeeList(List<String> reportSendOrgIds) {
        return reportSendOrgIds.stream().map(a -> {
            Employee employee = new Employee();
            employee.setId(a);
            return employee;
        }).collect(Collectors.toList());
    }

    /**
     * 构建消息上下文
     *
     * @param zdPageScene 页面场景对象
     * @return 消息上下文对象
     */
    private MessageContextDTO buildReportMessageContext(ZdPageScene zdPageScene) {
        List<String> reportSendOrgIds = validReportSendOrgIds(zdPageScene);
        List<Integer> noteTypes = zdPageScene.getNoteType();
        MessageContextDTO messageContextDTO = new MessageContextDTO();
        List<SendModeTypeEnum> sendModeTypeEnums = NoteTypeEnum.sendModeTypeEnumList(noteTypes);
        SendModeTypeEnum[] array = sendModeTypeEnums.toArray(new SendModeTypeEnum[0]);

        // 设置消息发送方式
        messageContextDTO.initSendMode(array);

        // 设置消息接收人
        messageContextDTO.setReceivers(reportSendOrgIds.stream().map(a -> {
            MessageReceiverTemplateDTO messageReceiver = new MessageReceiverTemplateDTO();
            messageReceiver.setEmpId(a);
            return messageReceiver;
        }).collect(Collectors.toList()));

        return messageContextDTO;
    }


    /**
     * 定时任务
     */
    @Override
    public void notice() {
        RLock lock = redissonClient.getLock(LOCK_KEY);
        boolean locked = false;
        try {
            // 持有30秒
            locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (locked) {
                executeTask();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("执行定时任务异常: {}", e.getMessage(), e);
        } finally {
            // 确保锁在所有执行路径上都被释放
            lock.unlock();
        }
    }

    @Override
    public List<Employee> findEmployeeByPageCode(String pageCode, LocalDate localDate) {
        LambdaQueryWrapper<ZdPageSceneTask> select = eqOrIn(ZdPageSceneTask::getPageCode, pageCode)
                .eq(ZdPageSceneTask::getFinishDate, localDate)
                .select(ZdPageSceneTask::getRecipient);
        String recipient = stringValue(LamWrapper.ins(select));
        if (StringUtils.isNotBlank(recipient)) {
            List<String> recipientList = JsonUtil.toJavaObjectList(recipient, String.class);
            if (CollectionUtils.isEmpty(recipientList)) {
                return Collections.emptyList();
            }
            ZdPageAuthDTO zdPageAuthDTO = new ZdPageAuthDTO();
            zdPageAuthDTO.setMemberOrgIdList(recipientList);
            zdPageAuthDTO.setMemberType(MemberTypeEnum.PERSON);
            return authService.findAuthEmpWithAll(Collections.singletonList(zdPageAuthDTO));
        }
        return authService.findSubmitEmp(pageCode);

    }

    @Override
    public boolean collectIsStop(String pageCode, String reportParam) {
        ZdPageScene zdPageScene = pageSceneDAO.getOne(LamWrapper.eqOrIn(ZdPageScene::getPageCode, pageCode));
        if (Objects.nonNull(zdPageScene)) {
            return checkIfCollectionIsStopped(zdPageScene, reportParam);
        } else {
            throw new BussinessException("场景设置不存在");
        }
    }

    /**
     * 检查表单收集是否已停止
     *
     * @param zdPageScene 页面场景
     * @param reportParam 报表参数
     * @return 是否已停止
     */
    private boolean checkIfCollectionIsStopped(ZdPageScene zdPageScene, String reportParam) {
        LocalDate reportDate = getReportDate(reportParam);
        SceneTypeEnum sceneType = zdPageScene.getSceneType();

        if (Objects.equals(sceneType, SceneTypeEnum.COMMON)) {
            return true;
        }

        LocalDate nowDate = LocalDate.now();
        SceneRepeatTypeEnum repeatType = zdPageScene.getRepeatType();

        if (Objects.equals(repeatType, SceneRepeatTypeEnum.DATE)) {
            return checkDailyCollectionStop(reportDate, nowDate);
        } else {
            return checkOtherCollectionStop(zdPageScene, reportDate, nowDate, repeatType);
        }
    }

    /**
     * 检查每日重复类型的表单收集是否已停止
     *
     * @param reportDate 报告日期
     * @param nowDate    当前日期
     * @return 是否已停止
     */
    private boolean checkDailyCollectionStop(LocalDate reportDate, LocalDate nowDate) {
        // 每天晚上六点
        if (!nowDate.isEqual(reportDate)) {
            // 假如不是同一天的话，那么就是停止了。
            return true;
        }
        // 当前时间大于结束时间，表示停止了
        return LocalTime.now().isAfter(endTime);
    }

    /**
     * 检查周/月等其他重复类型的表单收集是否已停止
     *
     * @param zdPageScene 页面场景
     * @param reportDate  报告日期
     * @param nowDate     当前日期
     * @param repeatType  重复类型
     * @return 是否已停止
     */
    private boolean checkOtherCollectionStop(ZdPageScene zdPageScene, LocalDate reportDate, LocalDate nowDate, SceneRepeatTypeEnum repeatType) {
        ZdRepeatCreator zdRepeatCreator = SpringFactory.getBean(repeatType.beanName(null), ZdRepeatCreator.class);
        LocalDate next = zdRepeatCreator.next(reportDate, zdPageScene);

        if (next.isEqual(nowDate)) {
            // 当天的没有停止。
            return false;
        }

        if (nowDate.isBefore(next)) {
            // 当前在下一个日期之前，那么就是没有停止。
            if (next.isEqual(next.minusDays(1))) {
                // 最后一天
                return LocalTime.now().isAfter(endTime);
            }
            // 没有停止
            return false;
        }
        // 当前时间没有在下一个日期之后，那么就是停止了。
        return true;
    }

    /**
     * 执行具体任务逻辑
     *
     * <p>该方法执行以下主要步骤：</p>
     * <ol>
     *     <li>获取当前日期时间</li>
     *     <li>查询并遍历需要执行的任务场景</li>
     *     <li>根据不同的重复类型（周/月/日）及节假日策略判断执行条件</li>
     *     <li>发送消息或更新下次执行日期</li>
     * </ol>
     *
     * @implNote 包含复杂的时间判断逻辑，涉及工作日计算
     * @see #sendMessage(ZdPageScene, LocalDate) 发送消息的具体实现
     * @see #updateNextExecuteDate(Long, LocalDate) 更新下次执行日期的方法
     */
    private void executeTask() {
        // 查询当日需执行的任务场景
        List<ZdPageScene> zdPageScenes = pageSceneDAO.getBaseMapper().findTask(LocalDate.now());
        if (CollectionUtils.isEmpty(zdPageScenes)) {
            return;
        }

        // 获取当前时间和日期
        LocalTime localTime = LocalTime.now();
        LocalDate now = LocalDate.now();

        // 遍历所有任务场景
        zdPageScenes.forEach(zdPageScene -> processScene(zdPageScene, localTime, now));
    }

    /**
     * 处理单个场景任务
     *
     * @param zdPageScene 页面场景
     * @param localTime   当前时间
     * @param now         当前日期
     */
    private void processScene(ZdPageScene zdPageScene, LocalTime localTime, LocalDate now) {
        LocalTime startTime = zdPageScene.getStartTime();

        // 只处理当前时间等于或晚于开始时间的场景
        if (!localTime.isBefore(startTime)) {
            LocalDate nextExecuteDate = zdPageScene.getNextExecuteDate();

            // 如果已设置下次执行日期
            if (Objects.nonNull(nextExecuteDate)) {
                processWithNextExecuteDate(zdPageScene, now, nextExecuteDate);
            } else {
                processByRepeatType(zdPageScene, now);
            }
        }
    }

    /**
     * 处理已设置下次执行日期的场景
     *
     * @param zdPageScene     页面场景
     * @param now             当前日期
     * @param nextExecuteDate 下次执行日期
     */
    private void processWithNextExecuteDate(ZdPageScene zdPageScene, LocalDate now, LocalDate nextExecuteDate) {
        // 如果是当天，则发送消息并重置下次执行日期
        if (now.isEqual(nextExecuteDate)) {
            YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();
            if (Objects.equals(YesOrNoEnum.YES, holidayStrategy) && shouldSkipDueToHolidayPolicy(zdPageScene, now, holidaysService.checkHolidays(ZdDateUtil.toDate(now)))) {
                return;
            }
            sendMessage(zdPageScene, now);
            // 需要将完成日期设置为空
            updateNextExecuteDate(zdPageScene.getId(), null);
        }
    }

    /**
     * 根据重复类型处理场景
     *
     * @param zdPageScene 页面场景
     * @param now         当前日期
     */
    private void processByRepeatType(ZdPageScene zdPageScene, LocalDate now) {
        SceneRepeatTypeEnum repeatType = zdPageScene.getRepeatType();

        switch (repeatType) {
            case WEEK:
                processWeeklyScene(zdPageScene, now);
                break;
            case MONTH:
                processMonthlyScene(zdPageScene, now);
                break;
            case DATE:
                processDailyScene(zdPageScene, now);
                break;
            default:
                break;
        }
    }

    /**
     * 处理每周重复的场景
     *
     * @param zdPageScene 页面场景
     * @param now         当前日期
     */
    private void processWeeklyScene(ZdPageScene zdPageScene, LocalDate now) {
        DayOfWeek dayOfWeek = now.getDayOfWeek();
        Integer weekDay = zdPageScene.getWeekDay();
        if (Objects.isNull(weekDay)) {
            // 假如为空的话，那么就周一
            weekDay = 1;
        }
        if (Objects.equals(weekDay, dayOfWeek.getValue())) {
            // 到了约定的日子
            // 判断当前是不是节假日
            boolean isHolidays = holidaysService.checkHolidays(ZdDateUtil.toDate(now));
            if (shouldSkipDueToHolidayPolicy(zdPageScene, now, isHolidays)) {
                return;
            }
            sendMessage(zdPageScene, now);
        }
    }

    /**
     * 处理每月重复的场景
     *
     * @param zdPageScene 页面场景
     * @param now         当前日期
     */
    private void processMonthlyScene(ZdPageScene zdPageScene, LocalDate now) {
        Integer monthDay = zdPageScene.getMonthDay();
        if (Objects.isNull(monthDay)) {
            // 假如为空的话，那么就一号
            monthDay = 1;
        }
        if (Objects.equals(monthDay, now.getDayOfMonth())) {
            // 到了约定的日子
            // 判断当前是不是节假日
            boolean isHolidays = holidaysService.checkHolidays(ZdDateUtil.toDate(now));
            if (shouldSkipDueToHolidayPolicy(zdPageScene, now, isHolidays)) {
                return;
            }
            sendMessage(zdPageScene, now);
        }
    }

    /**
     * 根据节假日策略判断是否应跳过当前日期
     *
     * @param zdPageScene 页面场景
     * @param now         当前日期
     * @param isHolidays  是否为节假日
     * @return true表示应该跳过（不发送消息），false表示不需要跳过
     */
    private boolean shouldSkipDueToHolidayPolicy(ZdPageScene zdPageScene, LocalDate now, boolean isHolidays) {
        if (isHolidays) {
            YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();
            // 节假日是否顺延
            if (Objects.equals(holidayStrategy, YesOrNoEnum.YES)) {
                // 调整后的日期
                LocalDate adjustDate = adjustForHolidays(now.plusDays(1));
                updateNextExecuteDate(zdPageScene.getId(), adjustDate);
                return true;
            }
        }
        return false;
    }

    /**
     * 调整日期以避开节假日（最多检查10天）
     *
     * @param date 需要检查的日期
     * @return 调整后的日期
     */
    private LocalDate adjustForHolidays(LocalDate date) {
        int times = CHECK_DATES;
        LocalDate adjustedDate = date;
        boolean isHolidays = true;

        while (--times >= 0 && isHolidays) {
            isHolidays = holidaysService.checkHolidays(ZdDateUtil.toDate(adjustedDate));
            if (isHolidays) {
                // 如果是节假日，延后一天
                adjustedDate = adjustedDate.plusDays(1);
            }
        }
        return adjustedDate;
    }

    /**
     * 处理每日重复的场景
     *
     * @param zdPageScene 页面场景
     * @param now         当前日期
     */
    private void processDailyScene(ZdPageScene zdPageScene, LocalDate now) {
        // 节假日是否顺延
        YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();

        // 处理每日且跳过节假日的场景
        if (Objects.equals(holidayStrategy, YesOrNoEnum.YES) && holidaysService.checkHolidays(ZdDateUtil.toDate(now))) {
            // 如果是节假日，设置明天执行
            updateNextExecuteDate(zdPageScene.getId(), now.plusDays(1));
            return;
        }
        // 发送消息
        sendMessage(zdPageScene, now);
    }

    /**
     * 发送消息
     *
     * <p>该方法执行以下主要步骤：</p>
     * <ol>
     *     <li>构建表单提交链接</li>
     *     <li>获取需要发送的人员列表</li>
     *     <li>构建消息上下文</li>
     *     <li>发送消息</li>
     *     <li>更新完成日期</li>
     * </ol>
     *
     * @param zdPageScene 场景
     * @param now         当前时间
     */
    private void sendMessage(ZdPageScene zdPageScene, LocalDate now) {
        // 获取需要发送的人员列表
        List<Employee> employeeList = authService.findSubmitEmp(zdPageScene.getPageCode());
        sendMessageBase(zdPageScene, now, employeeList, new MessageContextDTO(), getSubmitUrlFunction());
        updateFinishDate(zdPageScene.getId(), now);
        // 保存 发送消息的人员
        if (!CollectionUtils.isEmpty(employeeList)) {
            dao.remove(
                    eqOrIn(ZdPageSceneTask::getPageCode, zdPageScene.getPageCode())
                            .eq(ZdPageSceneTask::getFinishDate, now)
            );
            List<String> recipientList = employeeList.stream().map(Employee::getId).collect(Collectors.toList());
            ZdPageSceneTask zdPageSceneTask = new ZdPageSceneTask();
            zdPageSceneTask.setPageCode(zdPageScene.getPageCode());
            zdPageSceneTask.setFinishDate(now);
            zdPageSceneTask.setRecipient(JsonUtil.toJsonString(recipientList));
            zdPageSceneTask.setNoteType(zdPageScene.getNoteType());
            dao.save(zdPageSceneTask);
        }
    }

    /**
     * 发送消息
     *
     * <p>该方法执行以下主要步骤：</p>
     * <ol>
     *     <li>构建表单提交链接</li>
     *     <li>获取需要发送的人员列表</li>
     *     <li>构建消息上下文</li>
     *     <li>发送消息</li>
     *     <li>更新完成日期</li>
     * </ol>
     *
     * @param zdPageScene  场景
     * @param now          当前时间
     * @param employeeList 需要发送的人员列表
     */
    private void sendMessageBase(ZdPageScene zdPageScene, LocalDate now, List<Employee> employeeList, MessageContextDTO messageContextDTO, BiFunction<String, String, String> urlFunc) {
        // 如果没有配置接收人
        if (CollectionUtils.isEmpty(employeeList)) {
            log.warn("找不到可以提交的人员，场景主键：{}", zdPageScene.getId());
            return;
        }

        String pageCode = zdPageScene.getPageCode();
        String appCode = pageService.getAppCodeByCode(pageCode);

        // 提交地址（这里含有发送日期）
        String submitUrl = urlFunc.apply(appCode, pageCode);
        if (Objects.nonNull(now)) {
            submitUrl = submitUrl + "?" + FormConstant.REPORT_PARAM + "=" + getParam(now);
        }
        // 提交地址，这里含有
        String url = nocodeDomainProperties.getDomainUrl() + submitUrl;


        // 获取消息发送类型
        List<Integer> noteTypes = zdPageScene.getNoteType();

        List<SendModeTypeEnum> sendModeTypeEnums = NoteTypeEnum.sendModeTypeEnumList(noteTypes);

        // 直接发送（异步发送的问题，当有多个实例时，环境不一致，导致偶发性的发送失败问题。）
        messageContextDTO.setSyncFlag(true);
        // 设置消息标题和内容
        String title = messageContextDTO.getTitle();
        if (StringUtils.isBlank(title)) {
            messageContextDTO.setTitle("表单填写邀请");
        }
        // 表单的名字
        String name = pageService.getNameByCode(pageCode);
        // 系统消息
        if (sendModeTypeEnums.contains(SendModeTypeEnum.SENDMODE_SYSREMINDER)) {
            sendSystemMessage(messageContextDTO, submitUrl, name, employeeList);
        }
        Map<String, String> userFlagTokenMap = new HashMap<>();
        // 邮件消息
        if (sendModeTypeEnums.contains(SendModeTypeEnum.SENDMODE_EMAIL)) {
            sendEmailMessage(messageContextDTO, employeeList, url, userFlagTokenMap, name);
        }
        // 企微消息
        if (sendModeTypeEnums.contains(SendModeTypeEnum.SENDMODE_WXWORK)) {
            sendWxWorkMessage(messageContextDTO, name, employeeList, url, userFlagTokenMap);
        }
    }

    /**
     * 发送企业微信消息
     *
     * <p>该方法用于向指定员工列表发送企业微信消息通知，包含表单填写链接。</p>
     *
     * @param messageContextDTO 消息上下文对象，用于配置和发送消息
     * @param name              表单名称，用于消息内容显示
     * @param employeeList      需要接收消息的员工列表
     * @param url               表单提交的基础URL地址
     * @param userFlagTokenMap  用户标识令牌缓存映射，用于避免重复生成令牌
     * @implNote 消息内容使用文本卡片形式展示，包含可点击的表单链接
     * @implNote 为每个员工单独发送消息，确保个性化链接
     * @implNote 发送失败时会记录错误日志
     */
    private void sendWxWorkMessage(MessageContextDTO messageContextDTO, String name, List<Employee> employeeList, String url, Map<String, String> userFlagTokenMap) {
        messageContextDTO.setSendMode(SendModeTypeEnum.SENDMODE_WXWORK.getEnumItemValue());
        messageContextDTO.setContent("<div class=\"highlight\">" + name + "</div>");
        // 假如是企微的话，那么配置send_config
        WxWorkSpecificAttrDTO wxWorkSpecificAttrDTO = WxWorkSpecificAttrDTO.builder().build();
        // 设置消息类型
        wxWorkSpecificAttrDTO.setMsgType(WxConsts.KefuMsgType.TEXTCARD);
        wxWorkSpecificAttrDTO.setAppCode(zdCommonConfig.getAppKey());
        employeeList.forEach(a -> {
            String finalUrl = getUrlWithUserFlagToken(url, getUserFlagToken(a.getUserName(), userFlagTokenMap));
            // 设置卡片地址
            wxWorkSpecificAttrDTO.setTextCardUrl(finalUrl);
            MessageSendConfigDTO messageSendConfigDTO =
                    MessageSendConfigDTO.buildToMessageSendConfig(wxWorkSpecificAttrDTO);
            messageSendConfigDTO.setSendType(wxWorkSpecificAttrDTO.getSendType());

            // 设置额外配置
            messageContextDTO.setSendConfigs(Collections.singletonList(messageSendConfigDTO));
            // 构建发送人员
            MessageReceiverTemplateDTO messageReceiver = new MessageReceiverTemplateDTO();
            messageReceiver.setEmpId(a.getId());
            // 设置发送人员
            messageContextDTO.setReceivers(Collections.singletonList(messageReceiver));
            // 发送企微消息
            // 构建文本卡片消息
            WxCpMessage message = WxCpMessage.TEXTCARD()
                    .url(url)
                    .title(messageContextDTO.getTitle())
                    .description(messageContextDTO.getContent())
                    .build();
            // 启用ID转换功能
            message.setEnableIdTrans(true);
            List<MessageSendFailedReceiverDTO> messageSendFailedReceiverDTOS = messageTemplateExtService.sendMessage(messageContextDTO, message);
            // 添加失败的接收人
            if (!CollectionUtils.isEmpty(messageSendFailedReceiverDTOS)) {
                log.error("发送企微消息失败，失败接收人：{}", messageSendFailedReceiverDTOS);
            }
        });
    }

    /**
     * 发送邮件消息
     *
     * <p>该方法用于向指定员工列表发送包含表单链接的邮件消息通知。</p>
     *
     * @param messageContextDTO 消息上下文对象，用于配置和发送消息
     * @param employeeList      需要接收消息的员工列表
     * @param url               表单提交的基础URL地址
     * @param userFlagTokenMap  用户标识令牌缓存映射，用于避免重复生成令牌
     * @param name              表单名称，用于邮件内容显示
     * @implNote 消息内容包含可点击的表单链接，使用邮件消息模式发送
     * @implNote 为每个员工单独发送邮件，确保个性化链接
     * @implNote 发送失败时会记录错误日志
     */
    private void sendEmailMessage(MessageContextDTO messageContextDTO, List<Employee> employeeList, String url, Map<String, String> userFlagTokenMap, String name) {
        messageContextDTO.setSendMode(SendModeTypeEnum.SENDMODE_EMAIL.getEnumItemValue());
        employeeList.forEach(a -> {
            String finalUrl = getUrlWithUserFlagToken(url, getUserFlagToken(a.getUserName(), userFlagTokenMap));
            messageContextDTO.setContent("<a href=\"" + finalUrl + "\">" + name + "</a>");
            log.info("发送邮件消息，员工：{}，地址：{}", a.getUserName(), finalUrl);
            // 构建发送人员
            MessageReceiverTemplateDTO messageReceiver = new MessageReceiverTemplateDTO();
            messageReceiver.setEmpId(a.getId());
            // 设置发送人员
            messageContextDTO.setReceivers(Collections.singletonList(messageReceiver));
            // 发送系统消息
            List<MessageSendFailedReceiverDTO> messageSendFailedReceiverDTOS = messageTemplateExtService.sendMessage(messageContextDTO, null);
            // 添加失败的接收人
            if (!CollectionUtils.isEmpty(messageSendFailedReceiverDTOS)) {
                log.error("发送邮件消息失败，失败接收人：{}", messageSendFailedReceiverDTOS);
            }
        });
    }

    /**
     * 发送系统消息
     *
     * <p>该方法用于向指定员工列表发送系统消息通知，包含表单填写链接。</p>
     *
     * @param messageContextDTO 消息上下文对象，用于配置和发送消息
     * @param submitUrl         表单提交的相对URL地址
     * @param name              表单名称，用于消息标题显示
     * @param employeeList      需要接收消息的员工列表
     * @implNote 消息内容包含可点击的表单链接，使用系统消息模式发送
     * @implNote 发送失败时会记录错误日志
     */
    private void sendSystemMessage(MessageContextDTO messageContextDTO, String submitUrl, String name, List<Employee> employeeList) {
        // 假如包含系统消息 一次性发送消息，地址的话，相对地址
        messageContextDTO.setContent("<a href=\"" + submitUrl + "\">" + name + "</a>");
        messageContextDTO.setSendMode(SendModeTypeEnum.SENDMODE_SYSREMINDER.getEnumItemValue());
        // 设置消息接收人
        messageContextDTO.setReceivers(employeeList.stream().map(a -> {
            MessageReceiverTemplateDTO messageReceiver = new MessageReceiverTemplateDTO();
            messageReceiver.setEmpId(a.getId());
            // 其余属性不用设置，框架会自动填充
            return messageReceiver;
        }).collect(Collectors.toList()));
        // 发送系统消息
        List<MessageSendFailedReceiverDTO> messageSendFailedReceiverDTOS = messageTemplateExtService.sendMessage(messageContextDTO, null);
        // 添加失败的接收人
        if (!CollectionUtils.isEmpty(messageSendFailedReceiverDTOS)) {
            log.error("发送系统消息失败，失败接收人：{}", messageSendFailedReceiverDTOS);
        }

    }


    /**
     * 获取用户标识令牌
     *
     * <p>该方法用于获取或生成指定用户的标识令牌，采用缓存机制避免重复生成。</p>
     *
     * @param userName         用户名
     * @param userFlagTokenMap 用户令牌缓存映射
     * @return 用户标识令牌
     * @implNote 如果缓存中不存在该用户的令牌，则调用服务生成新的令牌并存入缓存
     */
    private String getUserFlagToken(String userName, Map<String, String> userFlagTokenMap) {
        String userFlagToken = userFlagTokenMap.get(userName);
        if (Objects.isNull(userFlagToken)) {
            userFlagToken = userFlagTokenService.generateUserFlagToken(userName);
            userFlagTokenMap.put(userName, userFlagToken);
        }
        return userFlagToken;
    }

    /**
     * 在URL后追加用户标识令牌参数
     *
     * <p>该方法用于在指定的URL后面添加用户标识令牌（userFlagToken）参数，
     * 以便在后续请求中识别和验证用户身份。</p>
     *
     * @param url           原始URL地址
     * @param userFlagToken 用户标识令牌
     * @return 拼接了用户标识令牌参数的完整URL
     * @implNote 使用"&"连接符将令牌参数追加到URL末尾
     */
    private String getUrlWithUserFlagToken(String url, String userFlagToken) {
        return ZdHttpUtil.makeParamURL(url, RequestContext.USER_FLAG_TOKEN, userFlagToken);
    }


    /**
     * 更新下次执行日
     *
     * @param id   主键
     * @param date 日期
     */
    private void updateNextExecuteDate(Long id, LocalDate date) {
        LambdaUpdateWrapper<ZdPageScene> update = Wrappers.<ZdPageScene>lambdaUpdate()
                .set(ZdPageScene::getNextExecuteDate, date)
                .eq(ZdPageScene::getId, id);
        pageSceneDAO.update(update);
    }

    /**
     * 更新完成日期
     *
     * @param id   主键
     * @param date 日期
     */
    private void updateFinishDate(Long id, LocalDate date) {
        LambdaUpdateWrapper<ZdPageScene> update = Wrappers.<ZdPageScene>lambdaUpdate()
                .set(ZdPageScene::getFinishDate, date)
                .eq(ZdPageScene::getId, id);
        pageSceneDAO.update(update);
    }

    /**
     * 加密日期参数生成访问令牌
     * 使用公钥加密日期字符串（格式：yyyyMMdd）
     *
     * @param date 待加密日期
     * @return 加密后的字符串
     */
    private String getParam(LocalDate date) {
        return encryption.encryptByPublicKey(ZdDateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 解密参数,获取报告日期
     *
     * @param param 参数
     * @return 报告日期
     */
    private LocalDate getReportDate(String param) {
        String s = encryption.decryptByPrivateKey(param);
        return ZdDateUtil.format(s, DatePattern.PURE_DATE_PATTERN);
    }


    /**
     * 分享微信群
     *
     * @param zdPageShareDTO 分享参数
     * @see ThreadPoolConfig
     */
    @Async("sinicubeAsyncThreadPool")
    @Override
    public void share(ZdPageShareDTO zdPageShareDTO) {
        ZdPageAuthDTO pageAuthDTO = new ZdPageAuthDTO();
        pageAuthDTO.setFormCode(zdPageShareDTO.getPageCode());
        pageAuthDTO.setMemberOrgIdList(zdPageShareDTO.getOrgIdList());
        MemberTypeEnum memberType = zdPageShareDTO.getMemberType();
        if (Objects.isNull(memberType)) {
            memberType = MemberTypeEnum.PERSON;
        }
        pageAuthDTO.setMemberType(memberType);
        List<Employee> authEmp = authService.findAuthEmpWithAll(Collections.singletonList(pageAuthDTO));
        ZdPageScene zdPageScene = new ZdPageScene();
        zdPageScene.setPageCode(zdPageShareDTO.getPageCode());
        // 通知方式
        List<Integer> noteType = zdPageShareDTO.getNoteType();
        if (CollectionUtils.isEmpty(noteType)) {
            noteType = Collections.singletonList(NoteTypeEnum.WX_WORK.getValue());
        }

        zdPageScene.setNoteType(noteType);
        sendMessageBase(zdPageScene, null, authEmp, new MessageContextDTO(), getSubmitUrlFunction());
    }

    public BiFunction<String, String, String> getSubmitUrlFunction() {
        return nocodeDomainProperties::getSubmitUrl;
    }

    public BiFunction<String, String, String> getReportUrlFunction() {
        return nocodeDomainProperties::getReportUrl;
    }


    @Value("${nocode.collect.form.end-time:18:00:00}")
    public void setEndTime(String endTimeStr) {
        endTime = LocalTime.parse(endTimeStr);
    }

}