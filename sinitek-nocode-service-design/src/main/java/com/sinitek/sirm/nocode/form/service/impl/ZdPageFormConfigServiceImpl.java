package com.sinitek.sirm.nocode.form.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.constant.CacheKeyConstant;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormConfigDAO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormConfigDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormConfig;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormConfigMapper;
import com.sinitek.sirm.nocode.form.mapstruct.ZdPageFormConfigMapstruct;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import com.sinitek.sirm.nocode.support.BaseDAO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-21 18:28:16
 * @description 针对表【zd_page_form_config(表单配置表)】的数据库操作Service实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("java:S125")
public class ZdPageFormConfigServiceImpl extends BaseDAO<ZdPageFormConfigMapper, ZdPageFormConfig, ZdPageFormConfigDAO>
        implements IZdPageFormConfigService {

    //private final DefaultWorkflowCreator defaultWorkflowCreator;

    private final ZdPageFormConfigMapstruct mapstruct;


    @Cacheable(value = CacheKeyConstant.FORM_CONFIG, key = "#p0")
    @Override
    public String getTableNameByFormCode(String formCode) {
        String tableName = dao.getBaseMapper().getTableNameByFormCode(formCode);
        if (StringUtils.isBlank(tableName)) {
            throw new BussinessException("");
        }
        return tableName;
    }

    @Override
    public ZdPageFormConfigDTO getByFormCode(String formCode) {
        if (StringUtils.isBlank(formCode)) {
            throw new BussinessException("3000004");
        }
        LambdaQueryWrapper<ZdPageFormConfig> queryWrapper = eqOrIn(ZdPageFormConfig::getFormCode, formCode);
        ZdPageFormConfig zdPageFormConfig = dao.getOne(queryWrapper);
        // 不能为空
        if (Objects.isNull(zdPageFormConfig)) {
            throw new BussinessException("");
        }
        String tableName = zdPageFormConfig.getTableName();
        if (StringUtils.isBlank(tableName)) {
            throw new BussinessException("");
        }
        return mapstruct.toDTO(zdPageFormConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createFormConfig(String appCode, String formCode, PageTypeEnum pageTypeEnum, String name) {
        // 获取所有空闲的表名称

        String tableName = getBaseMapper().getIdleTableName();
        if (StringUtils.isBlank(tableName)) {
            // 表单已经用完，请联系管理员！
            throw new BussinessException("3000003");
        }

        ZdPageFormConfig formConfig = new ZdPageFormConfig();
        formConfig.setFormCode(formCode);
        formConfig.setTableName(tableName);
        formConfig.setId(IdWorker.getId(formConfig));
        // 假如是 流程表单，则需要创建相关的流程，其中流程的key 就为 formCode
        if (Objects.equals(pageTypeEnum, PageTypeEnum.WORKFLOW_FORM)) {
            // 和wf_process 中的 processcode 相同，发起流程的时候就用这个
            formConfig.setProcesscode(formCode);
            //defaultWorkflowCreator.createWorkflow(appCode, formCode, formConfig.getId(), name);

        }
        dao.save(formConfig);


    }

    @Override
    public int getIdleTableLength() {
        return dao.getBaseMapper().getIdleTableLength();
    }


    @SuppressWarnings("java:S125")
    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM_CONFIG + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> formCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPageFormConfig> query = getQuery(formCodeList);
        List<ZdPageFormConfig> list = dao.list(query);
        if (CollectionUtils.isNotEmpty(list)) {
            // 表的名称集合
            List<String> tableNameList = new ArrayList<>();
            // 流程定义集合
            // List<TemplateParamDTO> templateParams = new ArrayList<>();
            ZdPageFormConfigServiceImpl that = SpringUtil.getBean(ZdPageFormConfigServiceImpl.class);
            Map<String, Object> map = new HashMap<>();
            list.forEach(p -> {
                try {
                    that.removeCacheByFormCode(p.getFormCode());
                } catch (Exception e) {
                    log.error("删除表单缓存失败！key:{}", p.getFormCode(), e);
                }

                String tableName = p.getTableName();
                if (StringUtils.isNotBlank(tableName)) {
                    tableNameList.add(tableName);
                    map.put(tableName, p.getFormCode());
                }
                String processcode = p.getProcesscode();
                if (StringUtils.isNotBlank(processcode)) {
                    //TemplateParamDTO templateParamDTO = new TemplateParamDTO();
                    //templateParamDTO.setProcesscode(processcode);
                    //templateParams.add(templateParamDTO);
                }

            });
            AppDeleteEvent deleteFormConfigEvent = new AppDeleteEvent(AppConstant.DE_FORM_DATA, tableNameList);
            deleteFormConfigEvent.setMap(map);
            SpringUtil.publishEvent(deleteFormConfigEvent);
            dao.remove(query);
            //defaultWorkflowCreator.deleteWorkflow(templateParams);
        }


    }

    @CacheEvict(value = CacheKeyConstant.FORM_CONFIG, key = "#p0")
    public void removeCacheByFormCode(String formCode) {
        log.info("删除表单缓存，key:{}", formCode);
    }

    /**
     * @param formCodeList 表单编码集合
     * @return 查询
     */
    private LambdaQueryWrapper<ZdPageFormConfig> getQuery(List<String> formCodeList) {
        return eqOrIn(ZdPageFormConfig::getFormCode, formCodeList).select(ZdPageFormConfig::getTableName, ZdPageFormConfig::getProcesscode, ZdPageFormConfig::getFormCode);
    }
}




