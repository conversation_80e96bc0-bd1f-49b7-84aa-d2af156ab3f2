package com.sinitek.sirm.nocode.message.config;

import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.message.feign.ISirmpmWxService;
import com.sinitek.sirm.nocode.message.job.ZdLocalWxWorkJob;
import com.sinitek.sirm.nocode.message.job.ZdSirmpmWxWorkJob;
import com.sinitek.sirm.nocode.message.support.ZdMessageWxSyncHandler;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.wxwork.service.impl.WxWorkAccountServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@Configuration
public class ZdMessageJobConfig {

    @Bean
    @ConditionalOnProperty(prefix = "global.app", name = "enable-xl-feature-flag", havingValue = "true")
    public ZdSirmpmWxWorkJob zdSirmpmWxWorkJob(ISirmpmWxService sirmpmWxService, ZdOrgUtil orgUtil, WxWorkAccountServiceImpl wxWorkAccountService, IOrgService orgService) {
        return new ZdSirmpmWxWorkJob(sirmpmWxService, orgUtil, wxWorkAccountService, orgService);
    }


    @Bean
    @ConditionalOnMissingBean(ZdMessageWxSyncHandler.class)
    public ZdLocalWxWorkJob zdLocalWxWorkJob(WxWorkAccountServiceImpl wxWorkAccountService, IOrgService orgService, ZdOrgUtil orgUtil) {
        return new ZdLocalWxWorkJob(wxWorkAccountService, orgService, orgUtil);
    }

}
