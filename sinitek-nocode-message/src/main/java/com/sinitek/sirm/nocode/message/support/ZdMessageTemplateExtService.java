package com.sinitek.sirm.nocode.message.support;

import com.sinitek.sirm.common.message.template.dto.MessageContextDTO;
import com.sinitek.sirm.common.message.template.dto.MessageReceiverTemplateDTO;
import com.sinitek.sirm.common.message.template.dto.MessageSendFailedReceiverDTO;
import com.sinitek.sirm.common.message.template.service.IMessageTemplateExtService;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.message.config.ZdWxCpConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 需要处理企微消息的扩展服务
 *
 * <AUTHOR>
 * @version 2025.0806
 * @since 1.0.0-SNAPSHOT
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ZdMessageTemplateExtService {


    private final IMessageTemplateExtService messageTemplateExtService;
    private final ZdMessageWxSyncHandler zdMessageWxSyncHandler;

    @Value("${global.app.enable-xl-feature-flag:false}")
    private boolean enableXlFeatureFlag;

    @Value("${nocode.message.wx-sender:}")
    private String wxSender;


    public List<MessageSendFailedReceiverDTO> sendMessage(MessageContextDTO context, WxCpMessage message) {
        if (Objects.nonNull(message) && (enableXlFeatureFlag || "self".equals(wxSender))) {
            List<MessageReceiverTemplateDTO> receivers = context.getReceivers();
            List<String> orgIdList = new ArrayList<>();
            for (MessageReceiverTemplateDTO receiver : receivers) {
                orgIdList.add(receiver.getEmpId());
            }
            Map<String, String> map = zdMessageWxSyncHandler.getAndSynchronize(orgIdList);
            String join = String.join(",", map.values());
            message.setToUser(join);
            WxCpService wxCpService = ZdWxCpConfig.getCpService(ZdCommonConstant.APP_KEY);
            if (Objects.isNull(wxCpService)) {
                log.error("获取企业微信服务失败,请配置智搭企微应用");
            } else {
                try {
                    wxCpService.getMessageService().send(message);
                } catch (Exception e) {
                    log.error("发送消息失败:{}", e.getMessage(), e);
                }
            }
        }
        return messageTemplateExtService.sendMessage(context);
    }

}
