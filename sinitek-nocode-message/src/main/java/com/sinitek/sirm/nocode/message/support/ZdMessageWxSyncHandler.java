package com.sinitek.sirm.nocode.message.support;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.sirm.framework.frontend.support.PageDataResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.tenant.utils.TenantConfigUtil;
import com.sinitek.sirm.wxwork.dto.WxWorkAccountQueryDTO;
import com.sinitek.sirm.wxwork.entity.WxWorkAccount;
import com.sinitek.sirm.wxwork.service.impl.WxWorkAccountServiceImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 企业微信账号同步处理器抽象类
 * <p>
 * 该类提供了一个通用的框架，用于同步企业微信账号信息。
 * 子类需要实现具体的同步逻辑。
 *
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public abstract class ZdMessageWxSyncHandler {
    private static final int PAGE_SIZE = 1000;
    protected final WxWorkAccountServiceImpl wxWorkAccountService;
    protected final IOrgService orgService;
    private final ZdOrgUtil orgUtil;

    /**
     * 构造函数，初始化企业微信账号服务和组织服务
     *
     * @param wxWorkAccountService 企业微信账号服务实现类
     * @param orgService           组织服务接口
     */
    protected ZdMessageWxSyncHandler(WxWorkAccountServiceImpl wxWorkAccountService, IOrgService orgService, ZdOrgUtil orgUtil) {
        this.wxWorkAccountService = wxWorkAccountService;
        this.orgService = orgService;
        this.orgUtil = orgUtil;
    }

    /**
     * 抽象同步方法，由子类实现具体的同步逻辑
     */
    public abstract void synchronous(int pageIndex);


    /**
     * 获取企业微信账号信息并同步
     * <p>
     * 该方法会根据传入的组织ID列表，查询企业微信账号信息，并同步到本地数据库。
     *
     * @param orgIdList 组织ID列表
     * @return 企业微信账号信息映射
     */
    public abstract Map<String, String> getAndSynchronize(List<String> orgIdList);


    /**
     * 批量保存企业微信账号信息
     * <p>
     * 该方法会比较传入的数据与现有数据，只保存新增的账号信息。
     * 同时会根据租户配置设置相应的租户ID。
     *
     * @param pageDataResult 分页数据结果
     * @param function       转换函数，用于将数据类型T转换为WxWorkAccount
     * @param <T>            数据类型
     */
    protected <T> Map<String, WxWorkAccount> saveBatch(PageDataResult<T> pageDataResult, Function<T, WxWorkAccount> function) {
        // 数据不一样的情况下，同步
        List<T> datalist = pageDataResult.getDatalist();
        if (CollectionUtils.isNotEmpty(datalist)) {
            function = function.andThen(a -> {
                if (!TenantConfigUtil.isEnable()) {
                    a.setTenantId(TenantConfigUtil.getRootCode());
                } else {
                    Employee employee = this.orgService.getEmployeeById(a.getOrgId());
                    a.setTenantId(employee.getTenantId());
                }
                return a;
            });
            Map<String, WxWorkAccount> map = datalist.stream().map(function).collect(Collectors.toMap(WxWorkAccount::getOrgId, r -> r, (r, g) -> r));
            Set<String> collect = map.keySet();
            LambdaQueryWrapper<WxWorkAccount> queryWrapper = Wrappers.<WxWorkAccount>lambdaQuery()
                    .in(WxWorkAccount::getOrgId, collect)
                    .select(WxWorkAccount::getOrgId);
            List<String> existOrgIdList = wxWorkAccountService.list(queryWrapper).stream().map(WxWorkAccount::getOrgId)
                    .collect(Collectors.toList());
            // 还剩下多少个
            existOrgIdList.forEach(map::remove);
            if (CollectionUtils.isNotEmpty(map)) {
                Collection<WxWorkAccount> values = map.values();
                wxWorkAccountService.saveBatch(values);
            }
            return map;
        }
        return new HashMap<>();
    }

    /**
     * 获取企业微信账号总数
     *
     * @return 企业微信账号总数
     */
    protected int getCount() {
        return wxWorkAccountService.count();
    }


    /**
     * 同步企业微信账号信息
     * <p>
     * 该方法通过分页方式获取企业微信账号信息，并与本地数据进行比较，
     * 如果数据不一致，则保存新增的账号信息。
     * 同时会根据租户配置设置相应的租户ID。
     *
     * @param pageIndex 页码
     * @param supplier  数据供应器，用于获取企业微信账号数据
     * @param function  转换函数，用于将数据类型T转换为WxWorkAccount
     * @param <T>       数据类型
     */
    protected <T> void synchronous(int pageIndex, Supplier<TableResult<T>> supplier, Function<T, WxWorkAccount> function) {
        WxWorkAccountQueryDTO param = new WxWorkAccountQueryDTO();
        param.setPageIndex(pageIndex);
        param.setPageSize(PAGE_SIZE);
        // 使用组织工具类执行请求，确保在正确的上下文中进行数据同步
        orgUtil.execute(requestContext -> {
            TableResult<T> result = supplier.get();
            if (Objects.nonNull(result)) {
                PageDataResult<T> pageDataResult = result.getData();
                if (Objects.nonNull(pageDataResult)) {
                    Integer totalsize = pageDataResult.getTotalsize();
                    saveBatch(pageDataResult, function);
                    // 如果还有更多数据，则递归调用同步方法
                    if (totalsize > pageIndex * param.getPageSize()) {
                        synchronous(pageIndex + 1);
                    }
                }
            }
        }, null);
    }

    /**
     * 根据组织ID列表查询企业微信ID映射
     *
     * @param orgIdList 组织ID列表
     * @return 组织ID与企业微信ID的映射关系，key为组织ID，value为企业微信ID
     */
    protected Map<String, String> findWxWorkIdListByOrgIdList(List<String> orgIdList) {
        if (CollectionUtils.isEmpty(orgIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<WxWorkAccount> queryWrapper = Wrappers.<WxWorkAccount>lambdaQuery()
                .in(WxWorkAccount::getOrgId, orgIdList)
                .select(WxWorkAccount::getOrgId, WxWorkAccount::getWxWorkId);
        List<WxWorkAccount> list = wxWorkAccountService.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(WxWorkAccount::getOrgId, WxWorkAccount::getWxWorkId));
    }
}