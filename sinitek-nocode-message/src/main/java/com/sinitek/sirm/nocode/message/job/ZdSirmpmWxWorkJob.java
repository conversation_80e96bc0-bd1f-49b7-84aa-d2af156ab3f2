package com.sinitek.sirm.nocode.message.job;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.sinitek.sirm.common.utils.IdEncryptUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.framework.frontend.support.PageDataResult;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.message.dto.SirmpmWxAccountRemoteDTO;
import com.sinitek.sirm.nocode.message.feign.ISirmpmWxService;
import com.sinitek.sirm.nocode.message.support.ZdMessageWxSyncHandler;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.wxwork.entity.WxWorkAccount;
import com.sinitek.sirm.wxwork.service.impl.WxWorkAccountServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 协力企微账号同步任务
 *
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class ZdSirmpmWxWorkJob extends ZdMessageWxSyncHandler {


    private static final int PAGE_SIZE = 1000;
    private static final int PARTITION_SIZE = 500;

    private final ISirmpmWxService sirmpmWxService;

    @Value("${nocode.wxwork.xl.sync-type:0}")
    private Integer syncType;

    public ZdSirmpmWxWorkJob(ISirmpmWxService sirmpmWxService, ZdOrgUtil orgUtil, WxWorkAccountServiceImpl wxWorkAccountService, IOrgService orgService) {
        super(wxWorkAccountService, orgService, orgUtil);
        this.sirmpmWxService = sirmpmWxService;
    }


    /**
     * 定时任务,半个小时同步一次
     */
    //@Scheduled(cron = "${nocode.wxwork.sync-time:0 0 0 * * ?}")
    public void synchronous() {
        if (Objects.equals(syncType, 0)) {
            synchronousAll();
        } else {
            synchronous(1);
        }
    }


    @Override
    public void synchronous(int pageIndex) {
        PageDataParam param = new PageDataParam();
        param.setPageIndex(pageIndex);
        param.setPageSize(PAGE_SIZE);
        synchronous(pageIndex, () -> sirmpmWxService.list(param), a -> {
            WxWorkAccount wxWorkAccount = new WxWorkAccount();
            wxWorkAccount.setOrgId(a.getOrgid());
            wxWorkAccount.setWxWorkId(a.getWxnum());
            return wxWorkAccount;
        });
    }

    @Override
    public Map<String, String> getAndSynchronize(List<String> orgIdList) {
        Map<String, String> map = findWxWorkIdListByOrgIdList(orgIdList);
        Set<String> set = map.keySet();
        // 假如是相等的话，那么就返回
        if (Objects.equals(set.size(), orgIdList.size())) {
            return map;
        }
        List<String> copyList = new ArrayList<>(orgIdList);
        copyList.removeAll(set);


        // 分批处理
        List<List<String>> partition = Lists.partition(copyList, PARTITION_SIZE);
        Map<String, WxWorkAccount> wxWorkAccountMap = new HashMap<>();
        partition.forEach(a -> wxWorkAccountMap.putAll(save(a)));
        wxWorkAccountMap.forEach((k, v) -> map.put(k, v.getWxWorkId()));
        return map;
    }

    private void synchronousAll() {
        // 只同步在职的。非在职的，不同步
        List<String> orgList = orgService.findAllEmployeesInSerivce().stream().map(Employee::getId).map(IdEncryptUtil::encrypt).collect(Collectors.toList());

        // 分批处理
        List<List<String>> partition = Lists.partition(orgList, PARTITION_SIZE);
        partition.forEach(this::save);

    }

    private Map<String, WxWorkAccount> save(List<String> orgList) {
        if (CollectionUtils.isEmpty(orgList)) {
            return new HashMap<>();
        }
        RequestResult<String> result = sirmpmWxService.findByOrgIdList(orgList);
        if (Objects.nonNull(result) && result.isSuccess()) {
            String data = result.getData();
            if (StringUtils.isNotBlank(data)) {
                String decrypt = IdEncryptUtil.decrypt(data);
                if (StringUtils.isNotBlank(decrypt)) {
                    List<SirmpmWxAccountRemoteDTO> javaObjectList = JsonUtil.toJavaObjectList(decrypt, SirmpmWxAccountRemoteDTO.class);
                    PageDataResult<SirmpmWxAccountRemoteDTO> pageDataResult = new PageDataResult<>();
                    pageDataResult.setDatalist(javaObjectList);
                    pageDataResult.setTotalsize(javaObjectList.size());
                    return saveBatch(pageDataResult, a -> {
                        WxWorkAccount wxWorkAccount = new WxWorkAccount();
                        wxWorkAccount.setOrgId(a.getOrgId());
                        wxWorkAccount.setWxWorkId(a.getWxWorkId());
                        return wxWorkAccount;
                    });
                }
            }
        }
        return new HashMap<>();
    }
}