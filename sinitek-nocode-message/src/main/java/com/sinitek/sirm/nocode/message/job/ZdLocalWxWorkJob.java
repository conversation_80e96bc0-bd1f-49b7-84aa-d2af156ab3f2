package com.sinitek.sirm.nocode.message.job;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.framework.frontend.support.PageDataResult;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.message.support.ZdMessageWxSyncHandler;
import com.sinitek.sirm.org.dto.EmployeeSearchDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.wxwork.entity.WxWorkAccount;
import com.sinitek.sirm.wxwork.service.impl.WxWorkAccountServiceImpl;

import java.util.List;
import java.util.Map;

/**
 * 本地微信同步任务
 *
 * <AUTHOR>
 * @version 2025.0819
 * @since 1.0.0-SNAPSHOT
 */

public class ZdLocalWxWorkJob extends ZdMessageWxSyncHandler {
    /**
     * 构造函数，初始化企业微信账号服务和组织服务
     *
     * @param wxWorkAccountService 企业微信账号服务实现类
     * @param orgService           组织服务接口
     */
    public ZdLocalWxWorkJob(WxWorkAccountServiceImpl wxWorkAccountService, IOrgService orgService, ZdOrgUtil orgUtil) {
        super(wxWorkAccountService, orgService, orgUtil);
    }

    @SuppressWarnings("java:S1186")
    @Override
    public void synchronous(int pageIndex) {

    }

    @Override
    public Map<String, String> getAndSynchronize(List<String> orgIdList) {
        Map<String, String> map = findWxWorkIdListByOrgIdList(orgIdList);
        if (orgIdList.size() == map.size()) {
            return map;
        }
        // 获取缺少的orgId
        orgIdList.removeAll(map.keySet());

        String orgIds = String.join(",", orgIdList);
        EmployeeSearchDTO employeeSearchDTO = new EmployeeSearchDTO();
        employeeSearchDTO.setEmpIds(orgIds);
        List<Employee> employeeList = orgService.findAllEmployees(employeeSearchDTO);
        if (CollectionUtils.isNotEmpty(employeeList)) {
            PageDataResult<Employee> pageDataResult = new PageDataResult<>();
            pageDataResult.setTotalsize(employeeList.size());
            pageDataResult.setDatalist(employeeList);
            Map<String, WxWorkAccount> wxWorkAccountMap = saveBatch(pageDataResult, a -> {
                WxWorkAccount wxWorkAccount = new WxWorkAccount();
                wxWorkAccount.setOrgId(a.getId());
                // 使用用户名称作为微信账号
                wxWorkAccount.setWxWorkId(a.getUserName());
                return wxWorkAccount;
            });
            wxWorkAccountMap.forEach((k, v) -> map.put(k, v.getWxWorkId()));
        }
        return map;
    }
}
