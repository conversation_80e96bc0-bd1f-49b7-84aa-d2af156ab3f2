package com.sinitek.sirm.nocode.wxwork.service.impl;

import com.sinitek.sirm.nocode.wxwork.dto.QyWxOrgObjectDTO;
import com.sinitek.sirm.nocode.wxwork.service.IZdWxUserService;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-08-08 15:23
 */
public class DefaultZdWxUserServiceImpl implements IZdWxUserService {


    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, QyWxOrgObjectDTO> getQyWxOrgObjectMapByOrgIds(List<String> orgIds) {
        return Collections.emptyMap();
    }

}
