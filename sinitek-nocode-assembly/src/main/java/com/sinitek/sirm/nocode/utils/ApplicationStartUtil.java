package com.sinitek.sirm.nocode.utils;

import cn.hutool.core.net.NetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @version 2025.0310
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class ApplicationStartUtil {
    private static final String SERVER_PORT = "server.port";
    
    private ApplicationStartUtil() {
    }

    public static ConfigurableApplicationContext startServiceApplication(Class<?> primarySource, String... args) {
        ConfigurableApplicationContext application = SpringApplication.run(primarySource, args);
        Environment env = application.getEnvironment();
        log.info("\n----------------------------------------------------------\n\t应用 '{}' 运行成功，访问端口: {}，API文档访问地址：https://{}:{}/doc.html\n----------------------------------------------------------", env.getProperty("spring.application.name"), env.getProperty(SERVER_PORT), NetUtil.getLocalhostStr(), env.getProperty(SERVER_PORT));
        return application;
    }

    public static ConfigurableApplicationContext startBackendApplication(Class<?> primarySource, String... args) {
        ConfigurableApplicationContext application = SpringApplication.run(primarySource, args);
        Environment env = application.getEnvironment();
        log.info("\n----------------------------------------------------------\n\t https://{}:{}{} 运行成功\n----------------------------------------------------------", NetUtil.getLocalhostStr(), env.getProperty(SERVER_PORT), env.getProperty("server.servlet.context-path"));
        return application;
    }
}