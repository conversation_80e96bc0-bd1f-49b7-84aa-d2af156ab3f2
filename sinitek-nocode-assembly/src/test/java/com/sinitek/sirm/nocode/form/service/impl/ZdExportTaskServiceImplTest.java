package com.sinitek.sirm.nocode.form.service.impl;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ComponentInfoDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.support.ctx.ZdFormDataExportContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.ZdComponentValueParserContainer;
import com.sinitek.sirm.nocode.test.tool.ZdBeanUnitTest;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 *
 * <AUTHOR>
 * @date 2025-09-01 15:41
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
public class ZdExportTaskServiceImplTest extends ZdBeanUnitTest {

    @Autowired
    private ZdComponentValueParserContainer parserContainer;

    @Autowired
    private ZdExportTaskServiceImpl service;

    @Test
    void testExeGenerateFailureReportExcel() throws Throwable {
        List<List> headList = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_export_task/headList.json"), List.class);
        List<ComponentInfoDTO> componentList = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_export_task/componentList.json"), ComponentInfoDTO.class);
        ZdFormDataExportParamDTO exportParam = JsonUtil.toJavaObject(
            readJsonFile("json/test_export_task/exportParam.json"), ZdFormDataExportParamDTO.class);
        List<ZdPageFormDataDTO> dataList = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_export_task/dataList.json"), ZdPageFormDataDTO.class);
        ZdFormDataExportContext ctx = JsonUtil.toJavaObject(
            readJsonFile("json/test_export_task/ctx.json"), ZdFormDataExportContext.class);
        List<SheetWriteHandler> sheetWriteHandlerList = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_export_task/sheetWriteHandlerList.json"),
            SheetWriteHandler.class);

        List<List<Object>> result = (List<List<Object>>) this.invokeMethod(
            this.service,
            "exeExport", headList, componentList,
            exportParam, dataList, ctx, sheetWriteHandlerList);

        System.out.println(JsonUtil.toJsonString(result));

        this.shouldHasSize(result, 1);
        this.shouldHasSize(result.get(0), 11);
    }
}
