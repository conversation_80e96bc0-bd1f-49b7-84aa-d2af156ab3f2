package com.sinitek.sirm.nocode.form.service.impl;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.support.handler.ZdOnceAbsoluteMergeStrategy;
import com.sinitek.sirm.nocode.test.tool.ZdBeanUnitTest;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import reactor.util.function.Tuple2;

/**
 *
 * <AUTHOR>
 * @date 2025-09-01 14:18
 */
@Slf4j
@ActiveProfiles("local")
public class ZdImportTaskServiceImplTest extends ZdBeanUnitTest {

    @Autowired
    private ZdImportTaskServiceImpl service;

    @Test
    void testExeGenerateFailureReportExcel() {
        List<Map> failureDataList = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_zd_import_task_service_impl/failureDataList.json"), Map.class);
        List<ZdImportFormHeaderFieldDTO> headerFields = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_zd_import_task_service_impl/headerFields.json"),
            ZdImportFormHeaderFieldDTO.class);
        List<ZdFormFieldDTO> excelHeaderList = JsonUtil.toJavaObjectList(
            readJsonFile("json/test_zd_import_task_service_impl/excelHeaderList.json"),
            ZdFormFieldDTO.class);
        ZdOnceAbsoluteMergeStrategy sheetWriteHandler = JsonUtil.toJavaObject(
            readJsonFile("json/test_zd_import_task_service_impl/sheetWriteHandler.json"),
            ZdOnceAbsoluteMergeStrategy.class);

        Tuple2<List<List<String>>, List<List<Object>>> result = (Tuple2<List<List<String>>, List<List<Object>>>) this.invokeMethod(
            this.service, ZdImportTaskServiceImpl.class,
            "exeGenerateFailureReportExcel", failureDataList, headerFields,
            excelHeaderList, sheetWriteHandler);

        List<List<String>> heads = result.getT1();
        List<List<Object>> datas = result.getT2();

        System.out.println(JsonUtil.toJsonString(heads));
        System.out.println(JsonUtil.toJsonString(datas));

        this.shouldHasSize(heads, 15);
        this.shouldHasSize(datas, 1);
        this.shouldHasSize(heads, datas.get(0).size());
    }

}
