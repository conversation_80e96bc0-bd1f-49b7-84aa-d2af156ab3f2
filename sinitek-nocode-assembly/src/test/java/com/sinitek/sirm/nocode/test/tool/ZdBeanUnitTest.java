package com.sinitek.sirm.nocode.test.tool;

import com.sinitek.sirm.nocode.SirmApplication;
import com.sinitek.sirm.nocode.test.tool.util.ZdSqlRunnerUtil;
import com.sinitek.sirm.nocode.test.tool.util.ZdTestDataUtil;
import java.io.IOException;
import java.lang.reflect.Method;
import java.sql.SQLException;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.runner.RunWith;
import org.spockframework.util.Nullable;
import org.spockframework.util.ReflectionUtil;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SirmApplication.class)
@AutoConfigureMockMvc
@SuppressWarnings("java:S2187")
public class ZdBeanUnitTest extends ZdUnitTestBase {

    @Autowired
    protected SqlSessionFactory sqlSessionFactory;

    @Autowired
    protected Environment environment;

    protected ZdSqlRunnerUtil sqlRunner;

    protected Object invokeMethod(Object target, Class<?> clazz, String methodName,
        @Nullable Object... args) {
        Method method = ReflectionUtil.getDeclaredMethodByName(clazz,
            methodName);
        method.setAccessible(true);
        return ReflectionUtil.invokeMethod(target, method, args);
    }

    protected Object invokeMethod(Object target, String methodName,
        @Nullable Object... args) throws Throwable {
        Class<?> clazz = AopUtils.getTargetClass(target);
        Method method = ReflectionUtil.getDeclaredMethodByName(clazz,
            methodName);
        return AopUtils.invokeJoinpointUsingReflection(target, method, args);
    }

    protected void initSqlRrunner() {
        this.sqlRunner = new ZdSqlRunnerUtil(this.sqlSessionFactory);
    }

    protected DataSource getDataSource() {
        return this.sqlSessionFactory.getConfiguration().getEnvironment().getDataSource();
    }

    protected void runSqlScript(String resource) throws SQLException, IOException {
        ZdTestDataUtil.runScript(
                this.getDataSource(),
                resource);
    }


}
