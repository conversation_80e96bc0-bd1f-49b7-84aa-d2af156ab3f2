package com.sinitek.sirm.nocode.page.mapstruct;

import com.sinitek.sirm.nocode.page.dto.ZdPagePublishDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPagePublish;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * 页面发布MapStruct转换器
 *
 * <AUTHOR>
 * @version 2025.0818
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPagePublishMapstruct {

    /**
     * 实体转DTO
     */
    @Mapping(source = "pageCode", target = "code")
    ZdPagePublishDTO toDTO(ZdPagePublish pagePublish);

    /**
     * DTO转实体
     */
    @InheritInverseConfiguration(name = "toDTO")
    ZdPagePublish toEntity(ZdPagePublishDTO dto);


    /**
     * 实体列表转DTO列表
     */
    List<ZdPagePublishDTO> toDTOList(List<ZdPagePublish> pagePublishList);

    /**
     * 更新实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(source = "code", target = "pageCode")
    void updateEntityFromDto(ZdPagePublishDTO dto, @MappingTarget ZdPagePublish entity);
}
