package com.sinitek.sirm.nocode.common.support.interceptor;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.common.utils.TokenFilterUtil;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class CustomClientTokenInterceptor implements RequestInterceptor {
    @Resource
    private TokenFilterUtil tokenFilterUtil;
    /**
     * 认证授权
     */
    @Value("${nocode.auth.authorization:}")
    private String authorization;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String token = RequestContext.getAccessToken();
        Map<String, Collection<String>> headers = requestTemplate.headers();
        if (StringUtils.isNotBlank(token)) {
            addIfNotExists(tokenFilterUtil.getTokenName(), token, requestTemplate, headers);
            String cookie = tokenFilterUtil.getTokenName() + "=" + token;
            addIfNotExists(RequestContext.COOKIE_KEY, cookie, requestTemplate, headers);
        }

        addIfNotExists(RequestContext.AUTHORIZATION_KEY, authorization, requestTemplate, headers);
    }

    /**
     * 如果指定的请求头不存在，则添加该请求头
     * <p>
     * 检查请求模板中是否已存在指定的请求头，如果不存在且值不为空，则添加该请求头
     * </p>
     *
     * @param key             请求头键名
     * @param value           请求头值
     * @param requestTemplate 请求模板对象
     * @param headers         当前已存在的请求头映射
     */
    private void addIfNotExists(String key, String value, RequestTemplate requestTemplate, Map<String, Collection<String>> headers) {
        if (StringUtils.isBlank(value)) {
            return;
        }
        Collection<String> strings = headers.get(key);
        if (CollectionUtils.isEmpty(strings)) {
            requestTemplate.header(key, value);
        }
    }
}
