package com.sinitek.sirm.nocode.common.properties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-21 17:24
 */
@Component
@ConfigurationProperties(
    prefix = "nocode.global"
)
@Data
@ApiModel(description = "低代码全局配置")
public class ZdGlobalProperties {
    
    // 全局配置常量
    private static final int DEFAULT_SINGLE_THREAD_DOWNLOAD_SIZE = 100;
    private static final int DEFAULT_DOWNLOAD_TIMEOUT = 60 * 1000;
    private static final String DEFAULT_TEMP_DIR = "/tmp";
    private static final String DEFAULT_TEMP_FILE_EXTENSION = "dat";
    private static final String DEFAULT_HELP_DOC_URL = "http://*************:18056/doc/sinitek-zhida/";

    @ApiModelProperty(value = "临时文件路径", required = true, example = "/tmp")
    private String tempDir = DEFAULT_TEMP_DIR;

    @ApiModelProperty(value = "平台管理员OrgId(只有系统参数缺失时才使用该配置)")
    private String adminOrgId;

    @ApiModelProperty(value = "临时文件后缀名")
    private String tempFileExtention = DEFAULT_TEMP_FILE_EXTENSION;

    @ApiModelProperty(value = "单线程下载容量")
    private Integer singleThreadDownloadSize = DEFAULT_SINGLE_THREAD_DOWNLOAD_SIZE;

    @ApiModelProperty(value = "下载超时时间(毫秒)")
    private Integer downloadTimeout = DEFAULT_DOWNLOAD_TIMEOUT;

    @ApiModelProperty(value = "帮助文档地址")
    private String helpDocUrl = DEFAULT_HELP_DOC_URL;

    @ApiModelProperty(value = "导出调试标识")
    private Boolean exportDebugFlag = false;

    @ApiModelProperty(value = "导入调试标识")
    private Boolean importDebugFlag = false;
}
