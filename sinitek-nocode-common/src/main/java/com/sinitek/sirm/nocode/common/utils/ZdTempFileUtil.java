package com.sinitek.sirm.nocode.common.utils;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.properties.ZdGlobalProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.Objects;

import static com.sinitek.sirm.nocode.common.constant.CommonMessageCodeConstant.CREATE_TEMP_FILE_FAILED;

/**
 * 智搭临时文件工具类
 *
 * <AUTHOR>
 * @date 2025-07-21 17:26
 */
@Slf4j
@RequiredArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ZdTempFileUtil {

    private static ZdGlobalProperties staticGlobalProperties;

    public static File createExcelTempFile(String prefix) {
        return createTempFile(prefix, ".xlsx");
    }

    public static File createTempFile(String prefix) {
        return createTempFile(prefix, null);
    }

    public static File createTempFile(String prefix, String suffix) {
        try {
            if (Objects.isNull(staticGlobalProperties)) {
                staticGlobalProperties = SpringFactory.getBean(ZdGlobalProperties.class);
            }
            String tempDir = staticGlobalProperties.getTempDir();
            if (StringUtils.isNotBlank(tempDir)) {
                return File.createTempFile(prefix, suffix, new File(tempDir));
            } else {
                return File.createTempFile(prefix, suffix);
            }
        } catch (IOException e) {
            log.error("创建临时文件 prefix: {},suffix: {} 失败 {}", prefix, suffix, e.getMessage(),
                    e);
            throw new BussinessException(CREATE_TEMP_FILE_FAILED);
        }
    }

}