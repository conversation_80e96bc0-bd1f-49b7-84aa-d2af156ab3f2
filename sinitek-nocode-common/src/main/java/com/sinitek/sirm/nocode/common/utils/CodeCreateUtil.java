package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.util.IdUtil;
import org.apache.commons.lang3.BooleanUtils;

import java.util.function.Predicate;


/**
 * 创建编码工具类
 *
 * <AUTHOR>
 * @version 2025.0328
 * @since 1.0.0-SNAPSHOT
 */
public class CodeCreateUtil {
    private static final Predicate<String> DEFAULT_NOT_EXIST_TEST = code -> false;

    private CodeCreateUtil() {
    }

    /**
     * 创建编码
     *
     * @param prefix    前缀
     * @param existTest 是否存在测试,假如存在的话，需要重新生成
     * @return 编码
     */
    public static String createCode(String prefix, Predicate<String> existTest) {
        String code = createUUID(prefix);
        while (BooleanUtils.isTrue(existTest.test(code))) {
            code = createUUID(prefix);
        }
        return code;
    }

    /**
     * 创建编码
     *
     * @param prefix 前缀
     * @return 编码
     */
    public static String createCode(String prefix) {
        return createCode(prefix, DEFAULT_NOT_EXIST_TEST);

    }

    /**
     * 创建编码
     *
     * @param prefix 前缀
     * @return 编码
     */
    private static String createUUID(String prefix) {
        return prefix + IdUtil.randomUUID().replace("-", "");
    }

}