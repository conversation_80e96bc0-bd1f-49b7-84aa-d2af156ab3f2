package com.sinitek.sirm.nocode.form.mapstruct;

import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchBaseFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageFormDataMapstruct {
    // 正向映射：ZdPageFormData → ZdPageFormDataDTO
    ZdPageFormDataDTO toDTO(ZdPageFormData pageFormData);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageFormData toEntity(ZdPageFormDataDTO dto);

    List<ZdPageFormDataDTO> toDTOList(List<ZdPageFormData> pageFormDataList);


    ZdFormDataSearchFieldDTO toSearchFieldDTO(ZdFormDataSearchBaseFieldDTO dto);

}
