package com.sinitek.sirm.nocode.ai.workflow;

import com.sinitek.sirm.common.utils.FileUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.workflow.config.ZdAiWorkflowWebclientConfig;
import com.sinitek.sirm.nocode.ai.workflow.util.ZdAiWorkflowWebclientUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.File;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.REQUEST_FAILED;

/**
 * <AUTHOR>
 * @date 2025-08-13 10:51
 */
public class TestSSERequest {

    private String MIND_BASE_URL = "http://192.168.21.121:54420";
    private String MIND_URI = "/mind/open-api/v2/chat/completions";
    private String MIND_TOKEN = "sinitek-a1bc9c8747d04f2f883c061bdc9ee344";

    private String FAST_GPT_BASE_URL = "http://192.168.23.202:3000/api";
    private String FAST_GPT_URI = "/v1/chat/completions";
    private String FAST_GPT_TOKEN = "fastgpt-iAc4iuCrCYqbSuafJzPYTrh2uvEp1Gn20On89b6DwHzOgoEdbEdBRJLszWC0ccnS2";

    public String readTxtFile(String filePath) {
        if (filePath.startsWith("txt")
                || filePath.startsWith("json")) {
            filePath = "/" + filePath;
        }
        File resource = new File(
                Objects.requireNonNull(TestSSERequest.class.getResource(filePath))
                        .getFile());
        return new String(FileUtil.getBytesFromFile(resource));
    }

    @SuppressWarnings("java:S2699")
    @Test
    @DisplayName("测试SSE请求")
    void testRequest() throws InterruptedException {

//        String baseUrl = MIND_BASE_URL;
//        String uri = MIND_URI;
//        String token = MIND_TOKEN;

        String baseUrl = FAST_GPT_BASE_URL;
        String uri = FAST_GPT_URI;
        String token = FAST_GPT_TOKEN;

        String message = this.readTxtFile("/json/q1_stream_detail.json");

        ParameterizedTypeReference<ServerSentEvent<String>> type =
                new ParameterizedTypeReference<ServerSentEvent<String>>() {
                };
        WebClient webClient = ZdAiWorkflowWebclientUtil.getWebClient(baseUrl,
                token,
                "", "",
                new ZdAiWorkflowWebclientConfig());

        try {
            Flux<ServerSentEvent<String>> flux = webClient.post()
                    .uri(uri)
                    .bodyValue(message)
                    .retrieve()
                    .onStatus((HttpStatus::isError),
                            clientResponse -> {
                                System.out.println(
                                        "clientResponse.statusCode: " + clientResponse.statusCode());
                                return Mono.error(
                                        new BussinessException(REQUEST_FAILED, "请求失败"));
                            })
                    .bodyToFlux(type);

            CountDownLatch latch = new CountDownLatch(1);
            flux.doOnNext(event -> {
                        // 处理业务逻辑
                        String eventName = event.event();
                        System.out.println(eventName);
                        System.out.println(event.data());
                    })
                    .doOnError(err -> {
                        try {
                            System.err.println("SSE 错误: " + err.getMessage());
                        } finally {
                            latch.countDown();
                        }
                    })
                    .doOnComplete(() -> {
                        try {
                            System.out.println("SSE 完成");
                        } finally {
                            latch.countDown();
                        }
                    })
                    .subscribe();

            latch.await();  // 等待流结束（非阻塞主线程）
        } catch (Exception e) {
            System.err.println("请求失败");
            e.printStackTrace();
        }

    }
}
