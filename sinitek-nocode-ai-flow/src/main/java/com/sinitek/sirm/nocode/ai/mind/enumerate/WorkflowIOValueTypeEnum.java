package com.sinitek.sirm.nocode.ai.mind.enumerate;

import lombok.Getter;

@Getter
public enum WorkflowIOValueTypeEnum {
    STRING("string"),
    NUMBER("number"),
    BOOLEAN("boolean"),
    OBJECT("object"),

    <PERSON>RAY_STRING("arrayString"),
    ARRAY_NUMBER("arrayNumber"),
    ARRAY_BOOLEAN("arrayBoolean"),
    ARRAY_OBJECT("arrayObject"),
    ARRAY_ANY("arrayAny"),
    ANY("any"),

    CHAT_HISTORY("chatHistory"),
    DATASET_QUOTE("datasetQuote"),

    DYNAMIC("dynamic"),

    // plugin special type
    SELECT_DATASET("selectDataset"),

    // abandon
    SELECT_APP("selectApp");

    private final String value;

    WorkflowIOValueTypeEnum(String value) {
        this.value = value;
    }
}