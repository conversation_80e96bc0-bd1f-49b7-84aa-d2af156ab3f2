package com.sinitek.sirm.nocode.ai.mind.core.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 创建app入参对象；对应type CreateAppBody
 */
@Data
@ApiModel("简单创建参数")
public class SimpleCreateAppParamDTO {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型")
    private String type; // AppTypeEnum

    @ApiModelProperty("命名空间")
    private String namespace;

    @ApiModelProperty("介绍")
    private String intro;

    @ApiModelProperty("全局变量")
    private List<ZnGlobalVariableItemTypeDTO> globalVariables;
}
