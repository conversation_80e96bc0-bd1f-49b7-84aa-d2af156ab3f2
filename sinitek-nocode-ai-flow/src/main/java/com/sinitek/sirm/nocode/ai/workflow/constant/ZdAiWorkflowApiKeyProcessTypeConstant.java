package com.sinitek.sirm.nocode.ai.workflow.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-13 15:45
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdAiWorkflowApiKeyProcessTypeConstant {

    // 手动输入
    public static final int MANUAL_INPUT = 1;

    // 自动生成
    public static final int AUTO_GENERATE = 2;

    // 使用已有key
    public static final int USE_EXISTING = 3;

}
