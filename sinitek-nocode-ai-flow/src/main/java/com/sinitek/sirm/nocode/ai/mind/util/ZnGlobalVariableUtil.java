package com.sinitek.sirm.nocode.ai.mind.util;

import cn.hutool.core.collection.ListUtil;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.ZnGlobalVariableItemTypeDTO;
import com.sinitek.sirm.nocode.ai.mind.enumerate.VariableInputEnum;
import com.sinitek.sirm.nocode.ai.mind.enumerate.WorkflowIOValueTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-20 10:50
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZnGlobalVariableUtil {

    public static List<ZnGlobalVariableItemTypeDTO> getGlobalVariableList() {
        return ListUtil.toList(
                buildTriggerIdVariableType(),
                buildFormCodeVariableType(),
                buildTypeVariableType(),
                buildRefVariableType(),
                buildChildRefVariableType(),
                buildSchemaVariableType(),
                buildModelVariableType()
        );
    }

    private static ZnGlobalVariableItemTypeDTO buildTriggerIdVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("111111")
                .key("triggerId")
                .label("触发Id")
                .type(VariableInputEnum.INPUT.getValue())
                .required(true)
                .description("工作流触发时传入的触发id")
                .valueType(WorkflowIOValueTypeEnum.STRING.getValue())
                .defaultValue(null)
                .build();
    }

    private static ZnGlobalVariableItemTypeDTO buildFormCodeVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("222222")
                .key("formCode")
                .label("表单编码")
                .type(VariableInputEnum.INPUT.getValue())
                .required(true)
                .description("工作流触发时传入的表单编码")
                .valueType(WorkflowIOValueTypeEnum.STRING.getValue())
                .defaultValue(null)
                .build();
    }

    private static ZnGlobalVariableItemTypeDTO buildTypeVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("333333")
                .key("type")
                .label("触发事件类型")
                .type(VariableInputEnum.INPUT.getValue())
                .required(true)
                .description("工作流触发时传入的触发工作流事件类型")
                .valueType(WorkflowIOValueTypeEnum.STRING.getValue())
                .defaultValue(null)
                .build();
    }

    private static ZnGlobalVariableItemTypeDTO buildRefVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("444444")
                .key("ref")
                .label("组件REF")
                .type(VariableInputEnum.INPUT.getValue())
                .required(true)
                .description("工作流触发时传入的触发工作流组件REF")
                .valueType(WorkflowIOValueTypeEnum.STRING.getValue())
                .defaultValue(null)
                .build();
    }

    private static ZnGlobalVariableItemTypeDTO buildChildRefVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("555555")
                .key("childRef")
                .label("子组件REF")
                .type(VariableInputEnum.INPUT.getValue())
                .required(false)
                .description("工作流触发时传入的触发工作流子组件REF(如果非子组件触发,该值可为空)")
                .valueType(WorkflowIOValueTypeEnum.STRING.getValue())
                .defaultValue(null)
                .build();
    }

    private static ZnGlobalVariableItemTypeDTO buildSchemaVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("666666")
                .key("schema")
                .label("页面")
                .type(VariableInputEnum.CUSTOM.getValue())
                .required(true)
                .description("工作流触发时传入的页面Schema")
                // TODO 后期需使用专门类型
                .valueType(WorkflowIOValueTypeEnum.OBJECT.getValue())
                .defaultValue(null)
                .build();
    }

    private static ZnGlobalVariableItemTypeDTO buildModelVariableType() {
        return ZnGlobalVariableItemTypeDTO.builder()
                .id("777777")
                .key("model")
                .label("数据")
                .type(VariableInputEnum.CUSTOM.getValue())
                .required(true)
                .description("工作流触发时传入的页面数据")
                // TODO 后期需使用专门类型
                .valueType(WorkflowIOValueTypeEnum.OBJECT.getValue())
                .defaultValue(null)
                .build();
    }
}
