package com.sinitek.sirm.nocode.ai.workflow.listener;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.ai.workflow.properties.ZdAiWorkflowProperties;
import com.sinitek.sirm.nocode.ai.workflow.service.IMindAppManageService;
import com.sinitek.sirm.nocode.ai.workflow.service.IZdAiWorkflowService;
import com.sinitek.sirm.nocode.page.dto.ZdPageDeleteEventSourceDTO;
import com.sinitek.sirm.nocode.page.event.PageDeleteEvent;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 监听表单删除事件,删除工作流
 *
 * <AUTHOR>
 * @date 2025-08-12 13:57
 */
@Slf4j
@Component
public class PageDeleteEventListener {

    @Autowired
    private IMindAppManageService appManageService;

    @Autowired
    private IZdAiWorkflowService zdAiWorkflowService;

    @Autowired
    private ZdAiWorkflowProperties zdAiWorkflowProperties;

    /**
     * 应用删除时会同步删除page,page会抛出删除事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void listenPageDelete(PageDeleteEvent event) {
        ZdPageDeleteEventSourceDTO source = event.getSource();
        List<String> codes = source.getCodes();
        boolean isEnableAiWorkflow = Objects.equals(Boolean.TRUE,
            this.zdAiWorkflowProperties.getEnabled());
        if (CollUtil.isNotEmpty(codes) && isEnableAiWorkflow) {
            log.info("监听到页面删除事件,同步删除工作流,页面编码: {}", codes);
            this.appManageService.deleteAppByNamespaces(codes);
            log.info("监听到页面删除事件,同步删除工作流绑定关系,页面编码: {}", codes);
            this.zdAiWorkflowService.deleteByFormCodes(codes);
        }
    }
}
