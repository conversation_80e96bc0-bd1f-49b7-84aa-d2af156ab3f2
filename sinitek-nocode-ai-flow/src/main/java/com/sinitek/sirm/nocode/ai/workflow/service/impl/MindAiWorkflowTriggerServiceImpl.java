package com.sinitek.sirm.nocode.ai.workflow.service.impl;

import com.sinitek.sirm.common.utils.Base64Utils;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.mind.properties.ZdMindProperties;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigInnerDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowTriggerParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.properties.ZdAiWorkflowProperties;
import com.sinitek.sirm.nocode.ai.workflow.service.IMindAiWorkflowTriggerService;
import com.sinitek.sirm.nocode.ai.workflow.service.IZdAiWorkflowService;
import com.sinitek.sirm.nocode.ai.workflow.support.AiWorkflowReqeustRunnable;
import com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowApiKeyUtil;
import com.sinitek.sirm.nocode.app.dto.ZdAppGetAccessTokenResponseBodyDTO;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;

import static com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowResponseWriteUtil.writeSseCompleteRes;
import static com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowResponseWriteUtil.writeSseErrRes;
import static com.sinitek.sirm.nocode.form.constant.ZdAiWorkflowConstant.DEFAULT_EXE_THREAD_POOL_NAME;

/**
 * <AUTHOR>
 * @date 2025-08-11 15:55
 */
@Slf4j
@Service
public class MindAiWorkflowTriggerServiceImpl implements IMindAiWorkflowTriggerService {

    @Autowired(required = false)
    private IZdPageFormInstanceSchemaService zdPageFormInstanceSchemaService;

    @Autowired
    private ZdAiWorkflowProperties properties;

    @Autowired
    private ZdMindProperties mindProperties;

    @Autowired
    private IZdAiWorkflowService aiWorkflowService;

    @Autowired
    private IZdAppService zdAppService;


    @Autowired
    @Qualifier(DEFAULT_EXE_THREAD_POOL_NAME)
    private Executor executor;

    @Override
    public SseEmitter trigger(ZdAiWorkflowTriggerParamDTO param) {
        Long bindId = param.getBindId();
        String formCode = param.getFormCode();
        String operatorId = param.getOperatorId();

        ZdAiWorkflowConfigInnerDTO bindInfo = this.aiWorkflowService.getBindInfo(bindId);
        Integer sseTimeout = this.properties.getSseTimeout();
        SseEmitter sseEmitter = new SseEmitter(Duration.ofSeconds(sseTimeout).toMillis());
        if (Objects.nonNull(bindInfo)) {
            log.info("触发工作流,表单编码: {},bindId: {},操作人: {}", formCode, bindId,
                    operatorId);

            Map<String, Object> schema = this.getSchema(param);
            String mindBaseUrl = this.mindProperties.getMindBaseUrl();
            String mindChatUri = this.mindProperties.getMindChatUri();

            ZdAppGetAccessTokenResponseBodyDTO accessTokenObj = this.zdAppService.accessTokenByFormCode(
                    formCode);
            if (Objects.nonNull(accessTokenObj)) {
                String appAccessToken = accessTokenObj.getAccessToken();
                String encryptApikey = bindInfo.getEncryptApikey();
                String apiKey = AiWorkflowApiKeyUtil.decryptApiKey(encryptApikey);
                String eventType = bindInfo.getEventType();
                AiWorkflowReqeustRunnable runnable = new AiWorkflowReqeustRunnable(
                        mindBaseUrl, mindChatUri,
                        param, sseEmitter, apiKey, eventType, schema, appAccessToken);
                this.executor.execute(runnable);
            } else {
                log.warn("根据表单 {} 无法获取应用AccessToken", formCode);
                writeSseErrRes(sseEmitter, new BussinessException(true, "表单所属应用不存在"));
                writeSseCompleteRes(sseEmitter);
                sseEmitter.complete();
            }
        } else {
            log.info("无须触发工作流,表单编码: {},bindId: {},操作人: {}", formCode, bindId,
                    operatorId);
            writeSseCompleteRes(sseEmitter);
            sseEmitter.complete();
        }
        return sseEmitter;
    }

    @SuppressWarnings({"squid:ReturnMapCheck"})
    private Map<String, Object> getSchema(ZdAiWorkflowTriggerParamDTO param) {
        String schemaId = param.getSchemaId();
        String schemaBase64 = param.getSchema();

        if (StringUtils.isNotBlank(schemaBase64)) {
            String decodeSchema = Base64Utils.decode(schemaBase64);
            return JsonUtil.toMap(decodeSchema);
        } else {
            if (Objects.nonNull(this.zdPageFormInstanceSchemaService)) {
                ZdFormInstanceSchemaCacheDTO schemaCache = this.zdPageFormInstanceSchemaService.getSchemaCache(
                        schemaId);
                if (Objects.nonNull(schemaCache)) {
                    return schemaCache.getSchema();
                } else {
                    log.warn("schemaId: {} 不存在", schemaId);
                }
            } else {
                // 除了单独运行所在模块,不会出现这种情况
                log.warn("zdPageFormInstanceSchemaService 不存在");
            }
        }
        return Collections.emptyMap();
    }
}
