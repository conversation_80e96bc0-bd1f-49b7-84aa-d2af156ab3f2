package com.sinitek.sirm.nocode.ai.workflow.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-13 11:13
 */
@Data
@NoArgsConstructor
@ApiModel("ai工作流webclient配置")
public class ZdAiWorkflowWebclientConfig {
    
    // WebClient配置常量
    private static final int DEFAULT_MAX_CONNECTIONS = 50;
    private static final int DEFAULT_TIMEOUT = 300;
    private static final int DEFAULT_ASYNC_RESPONSE_TIMEOUT = 90;
    private static final int DEFAULT_PENDING_ACQUIRE_TIMEOUT = 90;
    private static final int DEFAULT_MAX_IDLE_TIME = 30;

    @ApiModelProperty("ai连接超时时间（秒）")
    private Integer timeout = DEFAULT_TIMEOUT;

    @ApiModelProperty("ai最大连接数")
    private Integer maxConnections = DEFAULT_MAX_CONNECTIONS;

    @ApiModelProperty("ai异步响应超时（秒）")
    private Integer asyncResponseTimeout = DEFAULT_ASYNC_RESPONSE_TIMEOUT;

    @ApiModelProperty("等待获取连接的最大时间（秒）")
    private Integer pendingAcquireTimeout = DEFAULT_PENDING_ACQUIRE_TIMEOUT;

    @ApiModelProperty("连接在池中空闲多久后会被回收/关闭（秒）")
    private Integer maxIdleTime = DEFAULT_MAX_IDLE_TIME;

}