package com.sinitek.sirm.nocode.ai.mind.core.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("全局变量")
public class ZnGlobalVariableItemTypeDTO {

    @ApiModelProperty("主键-6位字符（前端提供），全局变量中唯一即可")
    private String id;

    @ApiModelProperty("变量名")
    private String key;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("标题")
    private String label;

    @ApiModelProperty("变量类型-对应前端的组件")
    private String type;// VariableInputEnum

    @ApiModelProperty("是否必填")
    private Boolean required;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("数据类型")
    private String valueType; // WorkflowIOValueTypeEnum

    @ApiModelProperty("默认值")
    private Object defaultValue;

    // input
    @ApiModelProperty("最大长度-input变量类型独有")
    private Integer maxLen;

    // numberInput
    @ApiModelProperty("最大值-numberInput变量类型独有")
    private Integer max;

    @ApiModelProperty("最小值-numberInput变量类型独有")
    private Integer min;

    // select
    @ApiModelProperty("选项列表-select变量类型独有")
    private List<ZnGlobalVariableOptionItemDTO> enums;
}
