package com.sinitek.sirm.nocode.ai.workflow.properties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-07 11:33
 */
@Data
@Component
@ApiModel("ai工作流配置")
@ConfigurationProperties(prefix = "nocode.ai.workflow")
public class ZdAiWorkflowProperties {
    
    // AI工作流配置常量
    private static final int DEFAULT_SSE_TIMEOUT = 300;
    private static final int DEFAULT_TIMEOUT = 300;
    private static final int DEFAULT_MAX_CONNECTIONS = 50;
    private static final int DEFAULT_ASYNC_RESPONSE_TIMEOUT = 90;
    private static final int DEFAULT_PENDING_ACQUIRE_TIMEOUT = 90;
    private static final int DEFAULT_MAX_IDLE_TIME = 30;
    private static final Long DEFAULT_API_KEY_MAX_USAGE_POINT = -1L;
    private static final String DEFAULT_API_KEY_NAME = "智搭应用";

    @ApiModelProperty("是否启用智脑")
    private Boolean enabled = true;

    @ApiModelProperty("智搭工作流触发sse连接超时时间(秒)")
    private Integer sseTimeout = DEFAULT_SSE_TIMEOUT;

    @ApiModelProperty("ai连接超时时间（秒）")
    private Integer timeout = DEFAULT_TIMEOUT;

    @ApiModelProperty("ai最大连接数")
    private Integer maxConnections = DEFAULT_MAX_CONNECTIONS;

    @ApiModelProperty("ai异步响应超时（秒）")
    private Integer asyncResponseTimeout = DEFAULT_ASYNC_RESPONSE_TIMEOUT;

    @ApiModelProperty("等待获取连接的最大时间（秒）")
    private Integer pendingAcquireTimeout = DEFAULT_PENDING_ACQUIRE_TIMEOUT;

    @ApiModelProperty("连接在池中空闲多久后会被回收/关闭（秒）")
    private Integer maxIdleTime = DEFAULT_MAX_IDLE_TIME;

    @ApiModelProperty("应用api key名称")
    private String apiKeyName = DEFAULT_API_KEY_NAME;

    @ApiModelProperty("应用api key最大使用点数")
    private Long apiKeyMaxUsagePoint = DEFAULT_API_KEY_MAX_USAGE_POINT;
}