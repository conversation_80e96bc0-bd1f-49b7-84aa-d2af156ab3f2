package com.sinitek.sirm.nocode.ai.mind.properties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-07 11:33
 */
@Data
@Component
@ApiModel("智脑配置")
@ConfigurationProperties(prefix = "nocode.mind")
public class ZdMindProperties {

    @ApiModelProperty("智脑模拟登录生成accessToken所需的用户名")
    private String userName;

    @ApiModelProperty("智脑模拟登录生成accessToken所需的用户密码")
    private String userPwd;

    @ApiModelProperty("智脑访问host地址")
    private String mindHost;

    @ApiModelProperty("智脑访问token")
    private String authorizationToken;

    @ApiModelProperty("智脑请求基础url")
    private String mindBaseUrl;

    @ApiModelProperty("智脑对话uri")
    private String mindChatUri;
}
