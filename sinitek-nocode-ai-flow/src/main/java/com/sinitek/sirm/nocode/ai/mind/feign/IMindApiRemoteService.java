package com.sinitek.sirm.nocode.ai.mind.feign;


import com.sinitek.sirm.nocode.ai.mind.common.support.ApiResponse;
import com.sinitek.sirm.nocode.ai.mind.support.FeignHeaderConfig;
import com.sinitek.sirm.nocode.ai.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2025-08-07 08:45
 */
@FeignClient(
    name = "${sinicube.mind.remote.service-name:CLOUD-MIND}",
    contextId = "mindCoreAppRemoteService",
    url = "${sinicube.mind.remote.url:}",
    configuration = FeignHeaderConfig.class
)
public interface IMindApiRemoteService {

    @PostMapping("/mind/open-api/support/openapi/create")
    @ApiOperation("创建应用API密钥")
    ApiResponse<String> createApiKey(@RequestBody CreateApiKeyRequestDTO param);

}
