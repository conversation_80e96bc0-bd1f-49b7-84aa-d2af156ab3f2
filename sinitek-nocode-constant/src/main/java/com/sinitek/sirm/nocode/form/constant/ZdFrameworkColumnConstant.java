package com.sinitek.sirm.nocode.form.constant;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-21 10:00
 */
@RequiredArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ZdFrameworkColumnConstant {

    /**
     * 主键
     */
    public static final String ID = "id";
    /**
     * 乐观锁
     */
    public static final String VERSION = "version";
    /**
     * 创建时间
     */
    public static final String CREATE_TIMESTAMP = "createTimeStamp";
    /**
     * 修改时间
     */
    public static final String UPDATE_TIMESTAMP = "updateTimeStamp";

}
