package com.sinitek.sirm.nocode.form.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class FormDataImportOrExportMessageCodeConstant {

    /**
     * 3000021001=导出任务不存在
     */
    public static final String EXPORT_TASK_NOT_EXIST = "3000021001";

    /**
     * 3000021002=导出失败
     */
    public static final String EXPORT_TASK_EXECUTE_FAILED = "3000021002";

    /**
     * 3000021003=无法获取表单配置
     */
    public static final String EXPORT_FORM_CONFIG_NOT_FOUND = "3000021003";

    /**
     * 3000021004=导出任务正在处理，稍后重试
     */
    public static final String EXPORT_TASK_PROCESSING = "3000021004";

    /**
     * 3000021005=导入文件为空
     */
    public static final String IMPORT_FILE_IS_EMPTY = "3000021005";

    /**
     * 3000021006=获取上传文件失败
     */
    public static final String IMPORT_GET_UPLOAD_FILE_FAILED = "3000021006";

    /**
     * 3000021007=Excel数据为空
     * 3000021008=Excel中导入数据为空
     * 3000021009=Excel表头存在空值
     * 3000021010=Excel解析失败
     */
    public static final String EXCEL_DATA_IS_EMPTY = "3000021007";
    public static final String EXCEL_IMPORT_DATA_IS_EMPTY = "3000021008";
    public static final String EXCEL_HEAD_IS_EMPTY = "3000021009";
    public static final String EXCEL_PARSE_FAILED = "3000021010";

    /**
     * 3000021011=导入任务不存在
     */
    public static final String IMPORT_TASK_NOT_EXIST = "3000021011";

    /**
     * 3000021012=导入任务正在处理，稍后重试
     */
    public static final String IMPORT_TASK_PROCESSING = "3000021012";

    /**
     * 3000021013=当前导入任务不存在新增数据,无需删除
     * 3000021014=当前导入任务新增数据已删除,请勿重复删除
     */
    public static final String IMPORT_TASK_ADDED_DATA_NOT_EXIST = "3000021013";
    public static final String IMPORT_TASK_ADDED_DATA_DELETED = "3000021014";

    /**
     * 3000021015=第【{0}】行第【{1}】列子表单中不允许存在合并数据
     */
    public static final String EXCEL_SUB_FORM_MERGE_DATA_NOT_ALLOWED = "3000021015";

    /**
     * 3000021016=数据异常
     */
    public static final String EXPORT_DATA_EXCEPTION = "3000021016";

}
