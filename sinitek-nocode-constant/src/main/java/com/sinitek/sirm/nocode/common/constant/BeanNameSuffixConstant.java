package com.sinitek.sirm.nocode.common.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * BeanName 后缀
 *
 * <AUTHOR>
 * @version 2025.0731
 * @since 1.0.0-SNAPSHOT
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BeanNameSuffixConstant {
    /**
     * 成员
     */
    public static final String MEMBER = "Member";
    /**
     * 数据权限
     */
    public static final String DATA_SCOPE = "DataScope";
    /**
     * 变量
     */
    public static final String VARIABLE = "Variable";
}
