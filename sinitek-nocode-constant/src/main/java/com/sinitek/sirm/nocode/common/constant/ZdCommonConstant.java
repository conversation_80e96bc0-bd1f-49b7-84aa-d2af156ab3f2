package com.sinitek.sirm.nocode.common.constant;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
public class ZdCommonConstant {
    private ZdCommonConstant() {
    }


    /**
     * 应用编码
     */
    public static final String APP_KEY = "zhida";
    /**
     * 名称
     */
    public static final String NAME = "零代码平台";

    /**
     * 枚举类名
     */
    public static final String ENUMERATE_CLASS = "com.sinitek.sirm.nocode.%s.enumerate.%s";


    /**
     * 应用的token名称
     */
    public static final String APPLICATION_TOKEN = "app_access_token";

    public static final String ORG_ID_HEADER = "opOrgId";

    /**
     * 忽略的属性
     */
    public static final String IGNORE_PROPERTY = "entityNameValue";
    /**
     * 没有用户组织ID
     */
    public static final String NO_PERSON_ORG_ID = "-999";
    /**
     * 匿名组织ID
     */
    public static final String ANONYMOUS_ORG_ID = "-1";

    /**
     * 重定向地址
     */
    public static final String REDIRECT_URL = "/frontend/api/zhida-open-api/nocode/login/redirect";

    /**
     * map初始化大小
     */

    public static final int MAP_INIT_SIZE = 16;

    /**
     * 变量模板
     */
    public static final String VAR_TEMPLATE = "${%s}";
}
