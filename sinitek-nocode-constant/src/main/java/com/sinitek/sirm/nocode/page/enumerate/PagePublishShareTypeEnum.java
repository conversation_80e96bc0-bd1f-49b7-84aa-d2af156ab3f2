package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0818
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum PagePublishShareTypeEnum implements BaseIntegerEnum {
    ALL(0, "所有"),
    SUBMIT(1, "提交页"),
    DATA_MANAGE(2, "管理页"),
    ;

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    PagePublishShareTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static PagePublishShareTypeEnum fromValue(Integer value) {
        return Arrays.stream(PagePublishShareTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
