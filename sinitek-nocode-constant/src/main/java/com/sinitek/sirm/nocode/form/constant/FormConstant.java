package com.sinitek.sirm.nocode.form.constant;

import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;

public class FormConstant {
    private FormConstant() {
    }

    /**
     * 表名称
     */
    public static final String TABLE_NAME = "tableName";
    /**
     * 表名称变量
     */
    public static final String TABLE_NAME_VAR = "${" + TABLE_NAME + "}";

    /**
     * 表单编码
     */
    public static final String FORM_CODE = "formCode";
    /**
     * 表单编码变量
     */
    public static final String FORM_CODE_VAR = String.format(ZdCommonConstant.VAR_TEMPLATE, FORM_CODE);
    /**
     * 报告参数
     */
    public static final String REPORT_PARAM = "reportParam";

}
