package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2025.0623
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "简易更新模式枚举")
@Getter
public enum SimpleUpdateModeEnum implements BaseIntegerEnum {
    // 未填写的字段置为空
    SET_NULL(1, "置空模式"),
    // 未填写的字段不修改
    NO_CHANGE(2, "保留模式");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    SimpleUpdateModeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static SimpleUpdateModeEnum fromValue(Integer value) {
        return Arrays.stream(SimpleUpdateModeEnum.values())
            .filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(NO_CHANGE);
    }
}
