package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0730
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "变量枚举")
@Getter
public enum VariableEnum implements BaseStringEnum {

    CURRENT_USER("${loginUser}", "当前登录人"),
    CURRENT_DEPARTMENTS("${departments}", "当前登录人所在部门"),

    ;
    /**
     * 值
     */
    @JsonValue
    private final String value;
    /**
     * 名称
     */
    private final String label;

    VariableEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static VariableEnum fromValue(String value) {
        return Arrays.stream(VariableEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
