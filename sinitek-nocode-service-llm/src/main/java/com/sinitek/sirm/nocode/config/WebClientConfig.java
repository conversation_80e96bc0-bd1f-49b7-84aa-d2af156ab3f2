package com.sinitek.sirm.nocode.config;

/**
 * WebClient 配置
 *
 * <AUTHOR>
 * @version 2025.0714
 * @since 1.0.0-SNAPSHOT
 */

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import io.netty.handler.timeout.ReadTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class WebClientConfig implements WebMvcConfigurer {
    // 默认等待获取连接的最大时间（秒）
    private static final int DEFAULT_PENDING_ACQUIRE_TIMEOUT = 900;
    // 默认连接在池中空闲多久后会被回收/关闭（秒）
    private static final int DEFAULT_MAX_IDLE_TIME = 30;

    @Resource
    private AiModelConfig aiModelConfig;


    // 配置 WebClient
    @Bean
    public Map<String, WebClient> webClient() {
        Map<String, WebClient> webClientMap = new HashMap<>();
        List<AiModelConfig.Model> models = aiModelConfig.getModels();
        if (CollectionUtils.isNotEmpty(models)) {
            for (AiModelConfig.Model model : models) {
                String server = model.getServer();
                String key = model.getKey();
                WebClient webClient = getWebClient(server, key);
                webClientMap.put(key, webClient);
            }
        }
        return webClientMap;
    }

    public WebClient getWebClient(String baseUrl, String modelKey) {
        String key = "ai-connection-pool-" + modelKey;
        return WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .filter((request, next) -> {
                    log.info("Request: {} {}", request.method(), request.url());
                    return next.exchange(request)
                            .doOnNext(response ->
                                    log.info("Response: {}", response.statusCode()));
                })
                .clientConnector(new ReactorClientHttpConnector(HttpClient.create(ConnectionProvider.builder(key)
                                .maxConnections(aiModelConfig.getMaxConnections())
                                .pendingAcquireTimeout(Duration.ofSeconds(DEFAULT_PENDING_ACQUIRE_TIMEOUT))
                                .maxIdleTime(Duration.ofSeconds(DEFAULT_MAX_IDLE_TIME))
                                .build())
                        .responseTimeout(Duration.ofMillis(aiModelConfig.getTimeout()))
                        .doOnConnected(conn ->
                                conn.addHandlerLast(new ReadTimeoutHandler(aiModelConfig.getTimeout(), TimeUnit.MILLISECONDS))
                        )))
                .build();
    }


    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(aiModelConfig.getAsyncResponseTimeout()); // 180秒
    }
}