package com.sinitek.sirm.nocode.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import com.sinitek.sirm.nocode.config.LlmServiceConfig;
import com.sinitek.sirm.nocode.constant.LlmGeneratorConstant;
import com.sinitek.sirm.nocode.constant.LlmMessageConstant;
import com.sinitek.sirm.nocode.constant.LlmPlatformConstant;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.service.ILlmService;
import com.sinitek.sirm.nocode.util.LlmTextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sinitek.sirm.nocode.constant.LlmGeneratorConstant.CHAT_GEN_SCHEMA;

/**
 * 统一的LLM服务实现
 *
 * <p>合并所有LLM功能到一个服务类中，消除抽象层次，实现简洁的调用结构。
 * 调用层次：Controller → UnifiedLlmService → HTTP请求</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>聊天对话：支持文本和图像输入</li>
 *   <li>智能代理：支持Agent模式调用</li>
 *   <li>文件上传：支持图像文件上传</li>
 *   <li>SQL生成：根据表单配置生成SQL</li>
 *   <li>图表生成：生成Mermaid图表代码</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2025/1/18
 */
@Slf4j
@Service
public class LlmServiceImpl implements ILlmService {

    // 流式响应数据前缀长度常量("data: "的长度)
    private static final int STREAM_DATA_PREFIX_LENGTH = 6;
    // HTTP状态码成功范围常量
    private static final int HTTP_STATUS_CODE_BASE = 100;
    private static final int HTTP_SUCCESS_STATUS_CODE_RANGE = 2;
    // JSON响应字段常量
    private static final String JSON_FIELD_ANSWER = "answer";
    private static final String JSON_FIELD_CONVERSATION_ID = "conversation_id";
    private static final String JSON_FIELD_ID = "id";
    private static final String JSON_FIELD_METADATA = "metadata";
    private static final String JSON_FIELD_USAGE = "usage";
    private static final String JSON_FIELD_PROMPT_TOKENS = "prompt_tokens";
    private static final String JSON_FIELD_COMPLETION_TOKENS = "completion_tokens";

    @Autowired
    private LlmServiceConfig llmConfig;

    @Autowired
    private IZdPageFormService pageFormService;

    @Autowired
    private IZdPageFormConfigService pageFormConfigService;

    /**
     * 聊天对话功能
     *
     * <p>支持文本和图像输入的对话功能，自动选择合适的模型。
     * 调用层次：chatCompletions → HTTP请求</p>
     */
    @Override
    public LlmChatCompletionsResponseDTO chatCompletions(LlmChatCompletionsRequestDTO request) {
        // 参数验证
        validateChatRequest(request);

        long startTime = System.currentTimeMillis();

        // 选择模型和构建请求
        ModelInfo modelInfo = selectChatModel(request);
        Map<String, Object> requestParams = buildChatRequestParams(request);
        requestParams.put("inputs", request.getParams() != null ? request.getParams() : new HashMap<>());

        log.info("开始聊天对话，模型: {}, 提示词长度: {}, 图像数量: {}",
                modelInfo.name, request.getPrompt().length(),
                request.getImages() != null ? request.getImages().size() : 0);

        // 发送HTTP请求
        String responseString = sendHttpRequest(modelInfo.url, modelInfo.apiKey, requestParams);

        // 解析响应
        JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));
        LlmChatCompletionsResponseDTO result = buildChatResponse(responseJson, startTime);

        log.info("聊天对话完成，响应长度: {}, 耗时: {}ms", result.getText().length(), result.getTime());
        return result;
    }

    /**
     * 智能代理功能
     *
     * <p>支持Agent模式的智能调用，包括图像输入和流式响应。
     * 调用层次：agentCompletions → 流式HTTP请求</p>
     */
    @Override
    public LlmAgentCompletionsResponseDTO agentCompletions(LlmAgentCompletionsRequestDTO request) {
        // 参数验证
        validateAgentRequest(request);

        long startTime = System.currentTimeMillis();

        // 选择代理模型
        ModelInfo modelInfo = selectAgentModel(request.getAgentId());
        Map<String, Object> requestParams = buildAgentRequestParams(request);

        log.info("开始智能代理调用，代理ID: {}, 提示词长度: {}, 图像数量: {}",
                request.getAgentId(), request.getPrompt().length(),
                request.getImages() != null ? request.getImages().size() : 0);

        // 发送流式HTTP请求
        LlmAgentCompletionsResponseDTO result = sendStreamingRequest(modelInfo.url, modelInfo.apiKey, requestParams, startTime);

        log.info("智能代理调用完成，响应长度: {}, 耗时: {}ms",
                result.getText().length(), result.getTime());
        return result;
    }

    /**
     * 文件上传功能
     *
     * <p>将文件上传到LLM平台，获取文件ID。
     * 调用层次：uploadFile → HTTP请求</p>
     */
    @Override
    public String uploadFile(File file) {
        // 参数验证
        validateUploadFile(file);

        if (!llmConfig.getSinitekImage().isValid()) {
            throw new BussinessException("图像模型配置无效");
        }

        String uploadUrl = llmConfig.getSinitekImage().getServer() + LlmPlatformConstant.UPLOAD_FILE;

        log.info("开始文件上传，文件: {}, 大小: {} bytes", file.getName(), file.length());

        try {
            HttpRequest request = HttpRequest.post(uploadUrl)
                    .header(RequestContext.AUTHORIZATION_KEY, "Bearer " + llmConfig.getSinitekImage().getApiKey())
                    .form("file", file);

            HttpResponse response = request.execute();

            //20 开头都是正确的，比如200，201，否则才需要报错
            if (response.getStatus() / HTTP_STATUS_CODE_BASE != HTTP_SUCCESS_STATUS_CODE_RANGE) {
                log.error("文件上传失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                throw new BussinessException("文件上传失败");
            }

            String responseBody = response.body();
            JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseBody));

            String fileId = responseJson.get("id").asText();
            log.info("文件上传成功，文件ID: {}", fileId);

            return fileId;

        } catch (Exception e) {
            log.error("文件上传失败，文件: {}, 错误: {}", file.getName(), e.getMessage(), e);
            throw new BussinessException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 图像描述功能
     *
     * <p>基于图像内容生成文本描述。
     * 调用层次：imageDesc → chatCompletions</p>
     */
    @Override
    public String imageDesc(List<File> images) {
        if (images == null || images.isEmpty()) {
            return "";
        }

        LlmChatCompletionsRequestDTO request = new LlmChatCompletionsRequestDTO();
        request.setPrompt("详细请描述一下这几张图片，最后返回一个json数组的数据");
        request.setImages(images);

        LlmChatCompletionsResponseDTO response = chatCompletions(request);
        return response.getText();
    }

    /**
     * SQL生成功能
     *
     * <p>根据表单配置和用户需求生成SQL查询语句。
     * 调用层次：formSqlGenerate → HTTP请求</p>
     */
    @Override
    public String formSqlGenerate(LlmChatSqlDTO request) {
        log.info("开始生成SQL，表单代码: {}, 需求: {}", request.getFormCode(), request.getPrompt());

        try {
            // 获取表单配置信息
            Map<String, Object> sqlGenParams = buildSqlGenerationParams(request);

            // 选择SQL生成模型
            if (!llmConfig.getSqlGenChat().isValid()) {
                throw new BussinessException("SQL生成服务配置无效");
            }

            String requestUrl = llmConfig.getSqlGenChat().getServer() + LlmPlatformConstant.MESSAGE_URL;
            Map<String, Object> requestParams = buildBaseRequestParams(request.getPrompt(), sqlGenParams, request.getSessionId());

            // 添加图像文件（如果有）
            if (request.getImages() != null && !request.getImages().isEmpty()) {
                addImageFilesToParams(requestParams, request.getImages());
            }

            // 发送HTTP请求
            String responseString = sendHttpRequest(requestUrl, llmConfig.getSqlGenChat().getApiKey(), requestParams);

            // 提取并格式化SQL
            String generatedSql = extractAndFormatSql(responseString);

            log.info("SQL生成完成，表单代码: {}, SQL长度: {}", request.getFormCode(), generatedSql.length());
            return generatedSql;

        } catch (Exception e) {
            log.error("SQL生成失败，表单代码: {}, 错误: {}", request.getFormCode(), e.getMessage(), e);
            throw new BussinessException("SQL生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * Mermaid图表生成功能
     *
     * <p>根据用户需求生成Mermaid图表代码。
     * 调用层次：mermaidGenerate → HTTP请求</p>
     */
    @Override
    public String mermaidGenerate(String prompt) {
        log.info("开始生成Mermaid图表，用户需求: {}", prompt);

        try {
            // 参数验证
            if (StringUtils.isBlank(prompt)) {
                throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
            }

            // 选择图表生成模型
            if (!llmConfig.getDiagramGenChat().isValid()) {
                throw new BussinessException("图表生成服务配置无效");
            }

            String requestUrl = llmConfig.getDiagramGenChat().getServer() + LlmPlatformConstant.MESSAGE_URL;
            Map<String, Object> requestParams = buildBaseRequestParams(prompt, null, null);

            // 发送HTTP请求
            String responseString = sendHttpRequest(requestUrl, llmConfig.getDiagramGenChat().getApiKey(), requestParams);

            // 提取并格式化Mermaid代码
            String generatedMermaid = extractAndFormatMermaid(responseString);

            log.info("Mermaid图表生成完成，代码长度: {}", generatedMermaid.length());
            return generatedMermaid;

        } catch (Exception e) {
            log.error("Mermaid图表生成失败，用户需求: {}, 错误: {}", prompt, e.getMessage(), e);
            throw new BussinessException("Mermaid图表生成失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证聊天请求参数
     */
    private void validateChatRequest(LlmChatCompletionsRequestDTO request) {
        if (request == null) {
            throw new BussinessException("请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getPrompt())) {
            throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
        }
    }

    /**
     * 验证代理请求参数
     */
    private void validateAgentRequest(LlmAgentCompletionsRequestDTO request) {
        if (request == null) {
            throw new BussinessException("代理请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getAgentId())) {
            throw new BussinessException("代理ID不能为空");
        }
        if (StringUtils.isBlank(request.getPrompt())) {
            throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
        }
    }

    /**
     * 验证上传文件
     */
    private void validateUploadFile(File file) {
        if (file == null) {
            throw new BussinessException("上传文件不能为空");
        }
        if (!file.exists()) {
            throw new BussinessException("上传文件不存在");
        }
        if (!file.isFile()) {
            throw new BussinessException("上传对象必须是文件");
        }
        if (file.length() == 0) {
            throw new BussinessException("上传文件不能为空文件");
        }
    }

    /**
     * 选择聊天模型
     */
    private ModelInfo selectChatModel(LlmChatCompletionsRequestDTO request) {
        // 如果有图像输入，使用图像模型

        if (CHAT_GEN_SCHEMA.equals(request.getChatId()) && llmConfig.getSchemaGenChat().isValid()) {
            return new ModelInfo("Schema生成模型",
                    llmConfig.getSchemaGenChat().getServer() + LlmPlatformConstant.MESSAGE_URL,
                    llmConfig.getSchemaGenChat().getApiKey());
        }
        // 选择文本模型（快速或标准）
        else if (request.isFastLlm() && llmConfig.getSinitekChatFast().isValid()) {
            return new ModelInfo("快速模型",
                    llmConfig.getSinitekChatFast().getServer() + LlmPlatformConstant.MESSAGE_URL,
                    llmConfig.getSinitekChatFast().getApiKey());
        } else {
            if (!llmConfig.getSinitekChat().isValid()) {
                throw new BussinessException("标准模型配置无效");
            }
            return new ModelInfo("标准模型",
                    llmConfig.getSinitekChat().getServer() + LlmPlatformConstant.MESSAGE_URL,
                    llmConfig.getSinitekChat().getApiKey());
        }
    }

    /**
     * 选择代理模型
     */
    private ModelInfo selectAgentModel(String agentId) {
        if (LlmGeneratorConstant.AGENT_GEN_SCHEMA.equals(agentId)) {
            if (!llmConfig.getSchemaGenAgent().isValid()) {
                throw new BussinessException("Schema生成代理配置无效");
            }
            return new ModelInfo("Schema生成代理",
                    llmConfig.getSchemaGenAgent().getServer() + LlmPlatformConstant.MESSAGE_URL,
                    llmConfig.getSchemaGenAgent().getApiKey());
        } else if (LlmGeneratorConstant.AGENT_MANUAL.equals(agentId)) {
            if (!llmConfig.getManualAgent().isValid()) {
                throw new BussinessException("教程生成代理配置无效");
            }
            return new ModelInfo("教程生成代理",
                    llmConfig.getManualAgent().getServer() + LlmPlatformConstant.MESSAGE_URL,
                    llmConfig.getManualAgent().getApiKey());
        } else {
            throw new BussinessException(LlmMessageConstant.LLM_NOT_EXIST);
        }
    }

    /**
     * 构建聊天请求参数
     */
    @SuppressWarnings({"squid:ReturnMapCheck"})
    private Map<String, Object> buildChatRequestParams(LlmChatCompletionsRequestDTO request) {
        Map<String, Object> params = buildBaseRequestParams(request.getPrompt(), new HashMap<>(), null);

        // 添加图像文件
        if (request.getImages() != null && !request.getImages().isEmpty()) {
            addImageFilesToParams(params, request.getImages());
        }

        return params;
    }

    /**
     * 构建代理请求参数
     */
    @SuppressWarnings({"squid:ReturnMapCheck"})
    private Map<String, Object> buildAgentRequestParams(LlmAgentCompletionsRequestDTO request) {
        Map<String, Object> params = new HashMap<>();
        params.put("response_mode", "streaming");
        params.put("user", "sinitek");
        params.put("inputs", request.getParams() != null ? request.getParams() : new HashMap<>());

        // 构建增强的查询内容
        String query = request.getPrompt();
        if (request.getWebInfo() != null) {
            query = query + "\n\n 可用的web服务信息：" + request.getWebInfo();
        }
        if (request.getEnums() != null) {
            query = query + "\n\n 可用的枚举信息：" + request.getEnums();
        }
        if (request.getModels() != null) {
            query = query + "\n\n 可用的模型：" + request.getModels();
        }
        if (request.getImageDesc() != null) {
            query = query + "\n\n 用户上传的图片内容：" + request.getImageDesc();
        }
        params.put("query", query);

        if (StringUtils.isNotBlank(request.getSessionId())) {
            params.put("conversation_id", request.getSessionId());
        }

        // 添加图像文件
        if (request.getImages() != null && !request.getImages().isEmpty()) {
            addImageFilesToParams(params, request.getImages());
        }

        return params;
    }

    /**
     * 构建基础请求参数
     */
    @SuppressWarnings({"squid:ReturnMapCheck"})
    private Map<String, Object> buildBaseRequestParams(String prompt, Map<String, Object> params, String sessionId) {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("response_mode", "blocking");
        requestParams.put("user", "sinitek");
        requestParams.put("inputs", params != null ? params : new HashMap<>());
        requestParams.put("query", prompt);

        if (StringUtils.isNotBlank(sessionId)) {
            requestParams.put("conversation_id", sessionId);
        }

        return requestParams;
    }

    /**
     * 添加图像文件到请求参数
     */
    private void addImageFilesToParams(Map<String, Object> params, List<File> images) {
        List<Map<String, Object>> files = new ArrayList<>();

        if (images != null) {
            for (File file : images) {
                String fileId = uploadFile(file);
                Map<String, Object> imageFile = new HashMap<>();
                imageFile.put("type", "image");
                imageFile.put("transfer_method", "local_file");
                imageFile.put("upload_file_id", fileId);
                files.add(imageFile);
            }
        }

        params.put("files", files);
    }

    /**
     * 构建SQL生成参数
     */
    @SuppressWarnings({"squid:ReturnMapCheck"})
    private Map<String, Object> buildSqlGenerationParams(LlmChatSqlDTO request) {
        String formCode = request.getFormCode();

        // 获取表单配置信息
        ZdPageFormDTO formConfig = pageFormService.view(formCode);
        if (formConfig == null) {
            throw new BussinessException("表单不存在，表单代码: " + formCode);
        }

        // 获取数据表名称
        String tableName = pageFormConfigService.getTableNameByFormCode(formCode);
        if (StringUtils.isBlank(tableName)) {
            throw new BussinessException("未找到表单对应的数据表，表单代码: " + formCode);
        }

        // 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", tableName);
        params.put("schema", formConfig.getPageData());

        log.debug("SQL生成参数构建完成 - 表名: {}, 表单配置已加载", tableName);
        return params;
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String url, String apiKey, Map<String, Object> requestParams) {
        log.debug("发送HTTP请求到: {}", url);
        log.trace("请求参数: {}", JsonUtil.toJsonString(requestParams));

        try {
            HttpRequest httpRequest = HttpRequest.post(url)
                    .header(RequestContext.AUTHORIZATION_KEY, "Bearer " + apiKey)
                    .header(RequestContext.CONTENT_TYPE, "application/json")
                    .body(JsonUtil.toJsonString(requestParams));

            HttpResponse response = httpRequest.execute();
            String responseBody = response.body();

            log.debug("收到HTTP响应，状态码: {}, 响应长度: {}", response.getStatus(), responseBody.length());
            return responseBody;

        } catch (Exception e) {
            log.error("HTTP请求失败，URL: {}, 错误: {}", url, e.getMessage(), e);
            throw new BussinessException("HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送流式HTTP请求
     */
    private LlmAgentCompletionsResponseDTO sendStreamingRequest(String url, String apiKey,
                                                                Map<String, Object> requestParams,
                                                                long startTime) {
        log.debug("发送流式HTTP请求到: {}", url);
        log.trace("请求参数: {}", JsonUtil.toJsonString(requestParams));

        try {
            HttpRequest httpRequest = HttpRequest.post(url)
                    .header(RequestContext.AUTHORIZATION_KEY, "Bearer " + apiKey)
                    .header(RequestContext.CONTENT_TYPE, "application/json")
                    .body(JsonUtil.toJsonString(requestParams));

            // 简化的流式响应处理
            StringBuilder fullResponse = new StringBuilder();
            String requestId = null;
            String sessionId = null;
            int inputTokens = 0;
            int outputTokens = 0;

            HttpResponse response = httpRequest.execute();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.bodyStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (StringUtils.isBlank(line) || !line.startsWith("data: ")) {
                        continue;
                    }

                    String jsonStr = line.substring(STREAM_DATA_PREFIX_LENGTH).trim();
                    if ("[DONE]".equals(jsonStr)) {
                        break;
                    }

                    JsonNode jsonNode = parseJsonLine(jsonStr);
                    if (jsonNode != null) {
                        // 提取请求ID
                        if (requestId == null && jsonNode.has("id")) {
                            requestId = jsonNode.get("id").asText();
                        }

                        // 提取会话ID
                        if (sessionId == null && jsonNode.has(JSON_FIELD_CONVERSATION_ID)) {
                            sessionId = jsonNode.get(JSON_FIELD_CONVERSATION_ID).asText();
                        }

                        // 提取响应内容
                        if (jsonNode.has(JSON_FIELD_ANSWER)) {
                            String answer = jsonNode.get(JSON_FIELD_ANSWER).asText();
                            if (StringUtils.isNotEmpty(answer)) {
                                answer = cleanThinkTags(answer);
                                fullResponse.append(answer);
                            }
                        }

                        // 提取Token统计
                        if (jsonNode.has(JSON_FIELD_METADATA) && jsonNode.get(JSON_FIELD_METADATA).has(JSON_FIELD_USAGE)) {
                            JsonNode usage = jsonNode.get(JSON_FIELD_METADATA).get(JSON_FIELD_USAGE);
                            if (usage.has("prompt_tokens")) {
                                inputTokens = Math.max(inputTokens, usage.get("prompt_tokens").asInt());
                            }
                            if (usage.has("completion_tokens")) {
                                outputTokens = Math.max(outputTokens, usage.get("completion_tokens").asInt());
                            }
                        }
                    }
                }
            }

            // 构建响应对象
            LlmAgentCompletionsResponseDTO result = new LlmAgentCompletionsResponseDTO();
            String responseText = fullResponse.toString();

            result.setRequestId(requestId);
            result.setText(responseText);
            result.setTime(System.currentTimeMillis() - startTime);
            result.setInputTokens(inputTokens);
            result.setOutputTokens(outputTokens);
            result.setSessionId(sessionId);
            result.setFinishFlag(isTaskCompleted(responseText) ? 1 : 0);

            return result;

        } catch (Exception e) {
            log.error("流式HTTP请求失败，URL: {}, 错误: {}", url, e.getMessage(), e);
            throw new BussinessException("流式HTTP请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建聊天响应对象
     */
    private LlmChatCompletionsResponseDTO buildChatResponse(JsonNode responseJson, long startTime) {
        LlmChatCompletionsResponseDTO result = new LlmChatCompletionsResponseDTO();

        // 提取并清理回答内容
        String answer = responseJson.get(JSON_FIELD_ANSWER).asText();
        answer = cleanThinkTags(answer);

        // 去除answer中 </think>以及</think>前面的内容
        int thinkEndIndex = answer.lastIndexOf("</think>");
        if (thinkEndIndex != -1) {
            answer = answer.substring(thinkEndIndex + "</think>".length());
        }

        String sessionId = null;
        // 提取会话ID
        if (responseJson.has(JSON_FIELD_CONVERSATION_ID)) {
            sessionId = responseJson.get(JSON_FIELD_CONVERSATION_ID).asText();
        }

        // 设置基本信息
        result.setRequestId(responseJson.get(JSON_FIELD_ID).asText());
        result.setText(answer);
        result.setSessionId(sessionId);
        result.setTime(System.currentTimeMillis() - startTime);

        // 设置Token使用统计
        if (responseJson.has(JSON_FIELD_METADATA)) {
            JsonNode usage = responseJson.get(JSON_FIELD_METADATA).get(JSON_FIELD_USAGE);
            result.setInputTokens(usage.get(JSON_FIELD_PROMPT_TOKENS).asInt());
            result.setOutputTokens(usage.get(JSON_FIELD_COMPLETION_TOKENS).asInt());
        }

        log.debug("聊天对话完成，耗时: {}ms, 输入tokens: {}, 输出tokens: {}",
                result.getTime(), result.getInputTokens(), result.getOutputTokens());

        return result;
    }

    /**
     * 从响应中提取并格式化SQL语句
     */
    private String extractAndFormatSql(String responseString) {
        try {
            JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));

            if (!responseJson.has(JSON_FIELD_ANSWER)) {
                throw new BussinessException("LLM响应中缺少answer字段");
            }

            String rawSql = responseJson.get(JSON_FIELD_ANSWER).asText();
            String cleanedSql = cleanThinkTags(rawSql);
            String formattedSql = LlmTextUtil.formatToSQL(cleanedSql);

            if (StringUtils.isBlank(formattedSql)) {
                throw new BussinessException("生成的SQL为空");
            }

            log.debug("SQL提取和格式化完成，最终SQL: {}", formattedSql);
            return formattedSql;

        } catch (Exception e) {
            log.error("SQL提取和格式化失败，原始响应: {}", responseString, e);
            throw new BussinessException("SQL提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从响应中提取并格式化Mermaid图表代码
     */
    private String extractAndFormatMermaid(String responseString) {
        try {
            JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));

            if (!responseJson.has(JSON_FIELD_ANSWER)) {
                throw new BussinessException("LLM响应中缺少answer字段");
            }

            String rawMermaid = responseJson.get(JSON_FIELD_ANSWER).asText();
            String cleanedMermaid = cleanThinkTags(rawMermaid);
            String formattedMermaid = LlmTextUtil.formatToMermaid(cleanedMermaid);

            if (StringUtils.isBlank(formattedMermaid)) {
                throw new BussinessException("生成的Mermaid图表为空");
            }

            log.debug("Mermaid图表提取和格式化完成，最终代码: {}", formattedMermaid);
            return formattedMermaid;

        } catch (Exception e) {
            log.error("Mermaid图表提取和格式化失败，原始响应: {}", responseString, e);
            throw new BussinessException("Mermaid图表提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理思考标签
     */
    private String cleanThinkTags(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        return text.replaceAll("(?s)<think>.*?</think>", "");
    }

    /**
     * 判断任务是否完成
     */
    private boolean isTaskCompleted(String responseText) {
        if (StringUtils.isEmpty(responseText)) {
            return false;
        }
        return responseText.contains("FinalAnswer") || responseText.contains("Final Answer") ||
                responseText.contains("//任务已完成") || responseText.contains("// 任务已完成") ||
                responseText.contains("function finalFunction(") || responseText.contains("function end(");
    }

    /**
     * 解析流式响应中的JSON行数据
     */
    private JsonNode parseJsonLine(String jsonStr) {
        try {
            return JsonUtil.getValueToJsonNode(JsonUtil.toMap(jsonStr));
        } catch (Exception e) {
            log.error("解析流式响应数据失败，跳过此行: {} - 错误: {}", jsonStr, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 模型信息内部类
     */
    private static class ModelInfo {
        final String name;
        final String url;
        final String apiKey;

        ModelInfo(String name, String url, String apiKey) {
            this.name = name;
            this.url = url;
            this.apiKey = apiKey;
        }
    }
} 