package com.sinitek.sirm.nocode.service.impl;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.app.service.IZdAppAiSettingService;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import com.sinitek.sirm.nocode.config.LlmServiceConfig;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.form.enumerate.AiMarkdownConversionTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiOutFormatEnum;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;
import com.sinitek.sirm.nocode.service.IZdAiAskService;
import com.sinitek.sirm.nocode.support.ask.DifyParamAndResponseHandler;
import com.sinitek.sirm.nocode.support.ask.base.ParamAndResponseHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0703
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZdAiAskServiceImpl implements IZdAiAskService {
    private final AiModelConfig aiModelConfig;
    private final LlmServiceConfig llmServiceConfig;
    private final IZdAppAiSettingService appAiSettingService;
    private final Map<String, WebClient> webClientMap;
    private final DifyParamAndResponseHandler defaultParamAndResponseHandler;

    private final ObjectProvider<List<ParamAndResponseHandler>> paramAndResponseHandlers;


    @SuppressWarnings("unchecked")
    @Override
    public Mono<RequestResult<LImChatReplyResponseBaseDTO>> ask(ZdPageFormAiQuestionSettingDTO setting) {
        String appKey = setting.getModelCode();
        AiModelConfig modelConfig = appAiSettingService.getByAppKeyConfig(appKey);
        AiModelConfig.ModelAppInfo modelAppInfo = validateAndGetModelAppInfo(modelConfig, appKey);
        ParamAndResponseHandler handler = getHandler(modelAppInfo);
        Map<String, Object> paramMap = handler.handleParam(setting, modelAppInfo);
        Mono<RequestResult<LImChatReplyResponseBaseDTO>> mono = handler.handleResponse(setting, modelAppInfo.getModel(), header(modelAppInfo, paramMap), Mono.class);
        AiOutFormatEnum outFormat = setting.getOutFormat();
        if (Objects.equals(outFormat, AiOutFormatEnum.MARKDOWN)) {
            RequestResult<LImChatReplyResponseBaseDTO> block = mono.block();
            if (Objects.nonNull(block)) {
                LImChatReplyResponseBaseDTO baseDTO = block.getData();
                String answer = baseDTO.getAnswer();
                setting.setInputContent(answer);
                LImChatReplyResponseBaseDTO answerMarkdown = ParamAndResponseHandler.fluxToString(markdownAsync(setting));
                log.info("markdown转换后: {}", answerMarkdown.getAnswer());
                RequestResult<LImChatReplyResponseBaseDTO> result = new RequestResult<>(answerMarkdown);
                return Mono.just(result);
            }
            RequestResult<LImChatReplyResponseBaseDTO> result = RequestResult.fail("-1");
            result.setMessage("markdown转换失败");
            return Mono.just(result);
        } else {
            return mono;
        }

    }

    /**
     * 获取适配指定模型配置的参数与响应处理器
     *
     * @param modelAppInfo 模型应用配置信息，用于确定处理器适配性
     * @return 适配的参数与响应处理器实例
     */
    private ParamAndResponseHandler getHandler(AiModelConfig.ModelAppInfo modelAppInfo) {
        // 初始化默认处理器
        ParamAndResponseHandler handler = defaultParamAndResponseHandler;

        // 从缓存获取可用处理器列表（避免重复创建）
        List<ParamAndResponseHandler> handlers = paramAndResponseHandlers.getIfAvailable();

        // 优先使用缓存中的适配处理器
        if (CollectionUtils.isNotEmpty(handlers)) {
            for (ParamAndResponseHandler paramAndResponseHandler : handlers) {
                // 发现首个适配当前模型配置的处理器时立即采用
                if (paramAndResponseHandler.support(modelAppInfo)) {
                    handler = paramAndResponseHandler;
                    break;
                }
            }
        }
        return handler;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Flux<LImChatReplyResponseBaseDTO> askAsync(ZdPageFormAiQuestionSettingDTO setting) {
        String appKey = setting.getModelCode();
        AiModelConfig modelConfig = appAiSettingService.getByAppKeyConfig(appKey);
        AiModelConfig.ModelAppInfo modelAppInfo = validateAndGetModelAppInfo(modelConfig, appKey);
        ParamAndResponseHandler handler = getHandler(modelAppInfo);
        Map<String, Object> paramMap = handler.handleParam(setting, modelAppInfo);
        Flux<LImChatReplyResponseBaseDTO> flux = handler.handleResponse(setting, modelAppInfo.getModel(), header(modelAppInfo, paramMap), Flux.class);
        AiOutFormatEnum outFormat = setting.getOutFormat();
        if (Objects.equals(outFormat, AiOutFormatEnum.MARKDOWN)) {
            LImChatReplyResponseBaseDTO baseDTO = ParamAndResponseHandler.fluxToString(flux);
            String answer = baseDTO.getAnswer();
            log.info("原始文档: {}", answer);
            setting.setInputContent(answer);
            return markdownAsync(setting);
        } else {
            return flux;
        }

    }


    @SuppressWarnings("unchecked")
    @Override
    public Flux<LImChatReplyResponseBaseDTO> markdownAsync(ZdPageFormAiQuestionSettingDTO setting) {

        Map<String, Object> map = defaultParamAndResponseHandler.handleParam(setting, new AiModelConfig.ModelAppInfo());
        map.put("query", setting.getInputContent());
        HashMap<Object, Object> inputs = new HashMap<>();
        inputs.put("rules", markdownRules(setting));
        map.put("inputs", inputs);
        LlmServiceConfig.ModelConfig markdownGenChat = llmServiceConfig.getMarkdownGenChat();
        if (Objects.isNull(markdownGenChat) || !markdownGenChat.isValid()) {
            throw new BussinessException("请配置markdown模型服务");
        }

        AiModelConfig.Model model = aiModelConfig.findModelByKey(AiSourceEnum.DIFY.getValue());
        WebClient.RequestHeadersSpec<?> header = header(model, null, markdownGenChat.getApiKey(), map);
        return defaultParamAndResponseHandler.handleResponse(setting, model, header, Flux.class);
    }

    @Override
    public Mono<RequestResult<LImChatReplyResponseBaseDTO>> markdown(ZdPageFormAiQuestionSettingDTO request) {
        return ParamAndResponseHandler.fluxToMono(markdownAsync(request));
    }

    private static String markdownRules(ZdPageFormAiQuestionSettingDTO setting) {
        List<Integer> markdownConversionType = setting.getMarkdownConversionType();
        if (CollectionUtils.isNotEmpty(markdownConversionType)) {
            // 添加markdown转换规则
            return "##补充规则:\n" + markdownConversionType.stream()
                    .map(AiMarkdownConversionTypeEnum::fromValue)
                    .filter(Objects::nonNull)
                    .map(AiMarkdownConversionTypeEnum::getRule)
                    .collect(Collectors.joining("\n"));

        }
        return "";
    }


    /**
     * 验证模型有效性并获取模型应用信息
     *
     * @param modelCode 模型编码
     * @return 模型应用信息
     * @throws BussinessException 当模型不存在时抛出异常
     */
    private AiModelConfig.ModelAppInfo validateAndGetModelAppInfo(AiModelConfig modelConfig, String modelCode) {
        AiModelConfig.ModelAppInfo modelAppInfo = modelConfig.findByAppKey(modelCode);
        if (Objects.isNull(modelAppInfo)) {
            log.error("未找到模型应用");
            throw new BussinessException("3000021", modelCode);
        }
        return modelAppInfo;
    }

    /**
     * 构建WebClient请求头配置，设置认证信息和内容类型。
     *
     * @param modelAppInfo 包含模型应用配置的对象，用于获取模型密钥和API密钥
     * @param bodyValue    请求体数据，将作为JSON格式发送
     * @return WebClient.RequestHeadersSpec<?> 配置好的请求头对象
     */
    private WebClient.RequestHeadersSpec<?> header(AiModelConfig.ModelAppInfo modelAppInfo, Map<String, Object> bodyValue) {
        return header(modelAppInfo.getModel(), modelAppInfo.findUrl(), modelAppInfo.getModelApp().getApiKey(), bodyValue);
    }


    private WebClient.RequestHeadersSpec<?> header(AiModelConfig.Model model, String url, String apiKey, Map<String, Object> bodyValue) {
        WebClient webClient = webClientMap.get(model.getKey());
        return webClient.post()
                .uri(StringUtils.isNotBlank(url) ? url : model.getUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestContext.AUTHORIZATION_KEY, "Bearer " + apiKey)
                .bodyValue(bodyValue)
                .accept(MediaType.TEXT_EVENT_STREAM);
    }
}
