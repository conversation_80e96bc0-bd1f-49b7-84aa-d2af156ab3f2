package com.sinitek.sirm.nocode.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0828
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应用创建请求DTO")
public class ZdAppSaveRequestDTO extends ZdAppSaveDTO {
    /**
     * 应用背景
     */
    @ApiModelProperty("应用背景")
    private String background;

    /**
     * 主题颜色
     */
    @ApiModelProperty("主题颜色")
    private String themeColor;

    /**
     * 应用图标类型
     */
    @ApiModelProperty("应用图标类型")
    private String iconfont;
}
