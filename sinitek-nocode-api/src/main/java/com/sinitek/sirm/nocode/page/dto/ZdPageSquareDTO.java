package com.sinitek.sirm.nocode.page.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.page.enumerate.PagePublishShareTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0718
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class ZdPageSquareDTO {
    @ApiModelProperty("应用编码")
    private String appCode;
    @ApiModelProperty("应用名称")
    private String appName;
    /**
     * 应用图标类型
     */
    @ApiModelProperty("应用图标类型")
    private String iconfont;

    /**
     * 主题颜色
     */
    @ApiModelProperty("主题颜色")
    private String themeColor;

    @JsonIgnore
    @ApiModelProperty("应用管理员orgId")
    private List<String> creatorIds;

    @ApiModelProperty("应用管理员名称")
    private List<String> creators;

    @ApiEnumProperty
    private PageTypeEnum pageType;
    @ApiModelProperty("页面名称")
    private String name;
    @ApiModelProperty("页面编码")
    private String code;
    @ApiModelProperty("页面id")
    private Long id;

    @ApiModelProperty("附件id")
    private Long attachmentId;

    @ApiModelProperty("是否收藏")
    private Boolean favoritesFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("发布时间")
    private Date publishTime;


    @ApiEnumProperty(enumClazz = PagePublishShareTypeEnum.class)
    private Integer shareType;


}
