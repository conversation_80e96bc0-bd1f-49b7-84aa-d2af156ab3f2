package com.sinitek.sirm.nocode.llm.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingDTO;
import com.sinitek.sirm.nocode.common.dto.ZdKeyAndNameDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * AI模型配置
 *
 * <AUTHOR>
 * @version 2025.0627
 * @since 1.0.0-SNAPSHOT
 */
@Component
@ConfigurationProperties(
        prefix = "nocode.ai"
)
@Data
@ApiModel(description = "AI模型配置")
public class AiModelConfig implements Serializable {
    private static final long serialVersionUID = 3461422915469197805L;
    
    // AI模型配置常量
    private static final long DEFAULT_TIMEOUT = 600000L;
    private static final int DEFAULT_MAX_CONNECTIONS = 50;
    private static final int DEFAULT_ASYNC_RESPONSE_TIMEOUT = 900000;

    @ApiModelProperty("ai模型列表")
    private List<Model> models;

    @JsonIgnore
    @ApiModelProperty("ai连接超时时间（毫秒）")
    private long timeout = DEFAULT_TIMEOUT;

    @JsonIgnore
    @ApiModelProperty("ai最大连接数")
    private Integer maxConnections = DEFAULT_MAX_CONNECTIONS;

    @JsonIgnore
    @ApiModelProperty("ai异步响应超时（毫秒）")
    private Integer asyncResponseTimeout = DEFAULT_ASYNC_RESPONSE_TIMEOUT;

    /**
     * 根据key查找模型
     *
     * @param key 模型key
     * @return 模型
     */
    public Model findModelByKey(String key) {
        if (CollectionUtils.isEmpty(models) || Objects.isNull(key)) {
            return null;
        }
        return models.stream().filter(model -> key.equals(model.getKey())).findFirst().orElse(null);
    }

    /**
     * 根据AI设置信息添加模型应用配置
     *
     * @param settingDTO AI设置信息，包含模型类型、应用名称、密钥等配置信息
     */
    public void add(ZdAppAiSettingDTO settingDTO) {
        // 根据AI源查找对应的模型配置
        Model model = findModelByKey(settingDTO.getAiSource().getValue());
        if (Objects.isNull(model)) {
            return;
        }

        // 获取模型类型key并查找对应的模型类型
        String typeKey = settingDTO.getTypeKey();
        List<ModelType> modelTypes = getModelTypes(model);
        ModelType modelType = findModelType(modelTypes, typeKey);

        // 如果找到对应的模型类型，则添加应用配置；否则处理缺失的模型类型
        if (Objects.nonNull(modelType)) {
            addModelAppToType(modelType, settingDTO);
        } else {
            handleMissingModelType(model, typeKey, settingDTO);
        }
    }

    /**
     * 获取模型的模型类型列表，如果模型类型列表为空，则创建一个新的列表并设置到模型中
     *
     * @param model AI模型配置对象
     * @return 模型的模型类型列表
     */
    private List<ModelType> getModelTypes(Model model) {
        List<ModelType> modelTypes = model.getModelTypes();
        if (Objects.isNull(modelTypes)) {
            modelTypes = new ArrayList<>();
            model.setModelTypes(modelTypes);
        }
        return modelTypes;
    }

    /**
     * 根据类型key查找模型类型
     *
     * @param modelTypes 模型类型列表
     * @param typeKey    类型key
     * @return 匹配的模型类型，未找到则返回null
     */
    private ModelType findModelType(List<ModelType> modelTypes, String typeKey) {
        return modelTypes.stream().filter(t -> typeKey.equals(t.getKey()))
                .findFirst().orElse(null);
    }

    /**
     * 将AI设置信息添加到指定的模型类型中
     *
     * @param modelType  模型类型对象，用于添加应用配置
     * @param settingDTO AI设置信息，包含应用名称、密钥等配置信息
     */
    private void addModelAppToType(ModelType modelType, ZdAppAiSettingDTO settingDTO) {
        List<ModelApp> modelApps = modelType.getModelApps();
        if (CollectionUtils.isEmpty(modelApps)) {
            modelApps = new ArrayList<>();
            modelType.setModelApps(modelApps);
        }
        ModelApp modelApp = new ModelApp();
        modelApp.setApiKey(settingDTO.getApiKey());
        modelApp.setKey(settingDTO.getId() + "");
        modelApp.setName(settingDTO.getAppName());
        modelApps.add(0, modelApp);
    }

    /**
     * 处理缺失的模型类型
     * <p>当根据typeKey在当前模型配置中找不到对应的模型类型时，尝试从该模型的所有模型类型中查找并添加</p>
     *
     * @param model      AI模型配置对象
     * @param typeKey    模型类型key
     * @param settingDTO AI设置信息，包含模型类型、应用名称、密钥等配置信息
     */
    private void handleMissingModelType(Model model, String typeKey, ZdAppAiSettingDTO settingDTO) {
        List<ModelType> allModelTypes = model.getAllModelTypes();
        if (!CollectionUtils.isEmpty(allModelTypes)) {
            // 从所有模型类型中查找匹配的模型类型
            ModelType modelType = allModelTypes.stream().filter(t -> typeKey.equals(t.getKey()))
                    .findFirst().orElse(null);
            if (Objects.nonNull(modelType)) {
                // 将找到的模型类型添加到当前模型的模型类型列表中，并重新调用add方法处理应用配置
                List<ModelType> modelTypes = getModelTypes(model);
                modelTypes.add(0, modelType);
                add(settingDTO);
            }
        }
    }

    @ApiModel(description = "AI模型基础信息")
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BaseInfo extends ZdKeyAndNameDTO {
        private static final long serialVersionUID = -2123791252961019575L;
        @JsonIgnore
        @ApiModelProperty("请求地址")
        private String url;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(description = "AI配置")
    public static class Model extends BaseInfo {
        private static final long serialVersionUID = 8616866005040540534L;
        @JsonIgnore
        @ApiModelProperty("请求地址")
        private String server;
        @ApiModelProperty("模型类型列表")
        private List<ModelType> modelTypes;

        @JsonIgnore
        @ApiModelProperty("这个模型下的所有模型类型")
        private List<ModelType> allModelTypes;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(description = "ai模型类型")
    public static class ModelType extends BaseInfo {
        private static final long serialVersionUID = -645251912238903281L;
        @ApiModelProperty("ai应用列表")
        private List<ModelApp> modelApps;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    @ApiModel(description = "ai应用")
    public static class ModelApp extends BaseInfo {
        private static final long serialVersionUID = 6805894172230055620L;
        @JsonIgnore
        @ApiModelProperty("应用的token")
        private String apiKey;
    }

    /**
     * 根据appKey查找模型应用
     *
     * @param appKey 应用key
     * @return 模型应用
     */
    public ModelAppInfo findByAppKey(String appKey) {
        if (appKey == null || CollectionUtils.isEmpty(models)) {
            return null;
        }
        for (Model model : models) {
            List<ModelType> modelTypes = model.getModelTypes();
            if (CollectionUtils.isEmpty(modelTypes)) {
                continue;
            }
            for (ModelType modelType : modelTypes) {
                List<ModelApp> modelApps = modelType.getModelApps();
                if (CollectionUtils.isEmpty(modelApps)) {
                    continue;
                }
                for (ModelApp modelApp : modelApps) {
                    if (appKey.equals(modelApp.getKey())) {
                        return new ModelAppInfo(model, modelType, modelApp);
                    }
                }
            }
        }
        return null;
    }


    @ApiModel(description = "模型应用参数")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ModelAppInfo {
        private Model model;
        private ModelType modelType;
        private ModelApp modelApp;

        /**
         * 查找url
         *
         * @return url
         */
        public String findUrl() {
            String url = Objects.nonNull(modelApp) ? modelApp.getUrl() : null;
            if (StringUtils.isNotBlank(url)) {
                return url;
            }
            url = Objects.nonNull(modelType) ? modelType.getUrl() : null;
            if (StringUtils.isNotBlank(url)) {
                return url;
            }
            url = model.getUrl();
            if (StringUtils.isNotBlank(url)) {
                return url;
            }
            return null;
        }


    }
}
