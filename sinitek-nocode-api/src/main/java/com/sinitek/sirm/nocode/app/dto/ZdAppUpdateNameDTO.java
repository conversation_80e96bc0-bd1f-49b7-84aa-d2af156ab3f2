package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.app.support.AppCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0331
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "修改应用名称参数DTO")
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdAppUpdateNameDTO extends ZdAppSaveDTO implements AppCodeSupplier {
    @ApiModelProperty(value = "应用编码", required = true, example = "app_24e8d143087149ab8b0d1114f1464816")
    @NotBlank(message = "应用编码不能为空")
    private String code;


}
