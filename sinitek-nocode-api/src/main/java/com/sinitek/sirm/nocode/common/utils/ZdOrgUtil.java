package com.sinitek.sirm.nocode.common.utils;

import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.constant.CacheKeyConstant;
import com.sinitek.sirm.nocode.common.feign.IUserFlagTokenLoginApiService;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.entity.Role;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.user.service.IUserFlagTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description 组织工具类
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Slf4j
public class ZdOrgUtil implements SmartInitializingSingleton {
    // HashMap初始容量常量
    private static final int HASH_MAP_INITIAL_CAPACITY = 8;

    @Resource
    private IOrgService orgService;
    @Resource
    private IUserFlagTokenLoginApiService userFlagTokenLoginApiService;
    @Resource
    private IUserFlagTokenService userFlagTokenService;
    @Resource
    private TokenFilterUtil tokenFilterUtil;
    @Value("${nocode.admin.role.name:管理员}")
    private String adminRoleName;

    private ZdOrgUtil self;


    @Cacheable(value = CacheKeyConstant.ROLE_CACHE_KEY, key = "#roleId")
    public List<String> getOrgIdsByRoleId(String roleId) {
        if (StringUtils.isNotBlank(roleId) && !Objects.equals(roleId, "0")) {
            return orgService.findEmployeeInserviceByRoleId(roleId).stream().map(Employee::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取用户token
     *
     * @param userName 用户名
     * @return 用户token
     */
    public String getToken(String userName) {
        if (StringUtils.isBlank(userName)) {
            userName = self.getAdminUserName();
        }
        String accessToken = self.getTokenByUserNameWithCache(userName);
        String cookie = tokenFilterUtil.getTokenName() + "=" + accessToken;
        UserDTO userDTO = tokenFilterUtil.checkToken(accessToken, "", "", cookie, "");
        if (Objects.isNull(userDTO)) {
            accessToken = refreshToken(userName);
        }
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取用户token失败！");
            throw new BussinessException("3000006");
        }
        return accessToken;


    }

    /**
     * 刷新用户token
     *
     * @param userName 用户名
     * @return 用户token
     */
    @CachePut(value = CacheKeyConstant.SINITEK_TOKEN_KEY, key = "#p0")
    public String refreshToken(String userName) {
        return getTokenByUserName(userName);
    }


    /**
     * 获取用户token
     *
     * @param userName 用户名
     * @return 用户token
     */
    @Cacheable(value = CacheKeyConstant.SINITEK_TOKEN_KEY, key = "#p0")
    public String getTokenByUserNameWithCache(String userName) {
        return getTokenByUserName(userName);
    }

    private String getTokenByUserName(String userName) {
        String userFlagToken = userFlagTokenService.generateUserFlagToken(userName);
        RequestResult<String> login = userFlagTokenLoginApiService.login(userFlagToken);
        if (!login.isSuccess()) {
            log.error("登录失败:用户名称:{},userFlagToken:{}", userName, userFlagToken);
            throw new BussinessException("3000005");
        }
        return login.getData();
    }

    /**
     * 获取管理员用户名
     *
     * @return String 管理员用户名，获取失败时返回null
     */

    @Cacheable(value = CacheKeyConstant.SINITEK_ADMIN_USER_NAME)
    public String getAdminUserName() {
        List<Role> adminRoles = orgService.findRoleByName(adminRoleName);
        if (CollectionUtils.isEmpty(adminRoles)) {
            log.error("管理员角色不存在");
            return null;
        }
        List<Employee> adminList = new ArrayList<>();
        for (Role role : adminRoles) {
            adminList = orgService.findEmployeeByRoleId(role.getObjid());
            if (!CollectionUtils.isEmpty(adminList)) {
                break;
            }
        }
        if (CollectionUtils.isEmpty(adminList)) {
            log.error("管理员不存在");
            return null;
        }
        Employee employee = adminList.get(0);
        return employee.getUserName();
    }

    /**
     * 执行请求
     *
     * @param consumer 请求处理函数
     */
    public void execute(Consumer<RequestContext> consumer, String userName) {
        RequestContext.execute(requestContext -> {
            RequestContext.setAccessToken(getTokenByUserName(userName));
            consumer.accept(requestContext);
        });

    }

    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param list 数据列表
     * @return 组织ID与名称的映射表
     */
    public Map<String, String> getOrgName(List<String> list) {
        return getOrgName(list, s -> s);
    }

    /**
     * 获取员工照片
     *
     * @param orgIdList 员工ID列表
     * @return 员工照片
     */
    public Map<String, String> getEmpPhotosByIds(List<String> orgIdList) {
        return orgService.getEmpPhotosByIds(orgIdList);
    }

    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param <T>           列表元素类型
     * @param list          数据列表
     * @param orgIdSupplier 组织ID提取函数
     * @return 组织ID与名称的映射表
     */
    public <T> Map<String, String> getOrgName(List<T> list, Function<T, String> orgIdSupplier) {
        return setOrgName(list, orgIdSupplier, null);
    }


    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param <T>           列表元素类型
     * @param list          数据列表
     * @param orgIdSupplier 组织ID提取函数
     * @param consumer      组织名称设置函数
     * @return 组织ID与名称的映射表
     */
    public <T> Map<String, String> setOrgName(List<T> list, Function<T, String> orgIdSupplier, BiConsumer<T, String> consumer) {
        Map<String, String> orgNameMapByOrgIdList = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return orgNameMapByOrgIdList;
        }
        Map<String, ArrayList<T>> map = new HashMap<>(HASH_MAP_INITIAL_CAPACITY);
        list.forEach(a -> {
            String apply = orgIdSupplier.apply(a);
            if (StringUtils.isNotBlank(apply)) {
                map.computeIfAbsent(apply, k -> new ArrayList<>()).add(a);
            }
        });
        if (CollectionUtils.isEmpty(map)) {
            return orgNameMapByOrgIdList;
        }
        Set<String> set = map.keySet();
        Map<String, String> resultMap = orgService.getOrgNameMapByOrgIdList(new ArrayList<>(set));
        if (Objects.nonNull(consumer)) {
            map.forEach((k, v) -> {
                String orgName = resultMap.get(k);
                v.forEach(a -> consumer.accept(a, orgName));
            });
        }
        return resultMap;
    }


    /**
     * 从列表中提取组织ID并构建组织名称映射
     *
     * @param <T>           列表元素类型
     * @param list          数据列表
     * @param orgIdSupplier 组织ID提取函数
     * @param consumer      组织名称设置函数
     * @return 组织ID与名称的映射表
     */
    @SuppressWarnings("unchecked")
    public <T> Map<String, String> setOrgNameCustom(List<T> list, Function<T, Object> orgIdSupplier, BiConsumer<T, Map<String, String>> consumer) {
        Map<String, String> orgNameMapByOrgIdList = new HashMap<>();
        if (CollectionUtils.isEmpty(list)) {
            return orgNameMapByOrgIdList;
        }
        Set<String> orgIdSet = new HashSet<>();

        list.forEach(a -> {
            Object apply = orgIdSupplier.apply(a);
            List<String> orgIdList = new ArrayList<>();
            if (apply instanceof String) {
                orgIdList.add((String) apply);
            } else if (apply instanceof List) {
                orgIdList = (List<String>) apply;
            } else if (apply instanceof Long) {
                orgIdList.add(String.valueOf(apply));
            }
            if (CollectionUtils.isEmpty(orgIdList)) {
                return;
            }
            orgIdList = orgIdList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            orgIdSet.addAll(orgIdList);
        });
        if (CollectionUtils.isEmpty(orgIdSet)) {
            return orgNameMapByOrgIdList;
        }
        Map<String, String> resultMap = orgService.getOrgNameMapByOrgIdList(new ArrayList<>(orgIdSet));
        if (Objects.nonNull(consumer)) {
            list.forEach(k -> consumer.accept(k, resultMap));
        }
        return resultMap;

    }

    @Override
    public void afterSingletonsInstantiated() {
        self = SpringFactory.getBean(ZdOrgUtil.class);
    }
}
