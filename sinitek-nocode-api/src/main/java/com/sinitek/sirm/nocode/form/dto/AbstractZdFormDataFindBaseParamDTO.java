package com.sinitek.sirm.nocode.form.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0819
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单数据搜索参数")
@Data
public abstract class AbstractZdFormDataFindBaseParamDTO implements FormCodeSupplier {

    @ApiModelProperty(value = "表单编码", required = true)
    @NotBlank(message = "表单编码不能为空")
    private String formCode;

    @NotEmpty(message = "搜索字段不能为空")
    @ApiModelProperty("搜索字段")
    private List<ZdFormDataSearchBaseFieldDTO> searchField;


    @JsonIgnore
    @ApiModelProperty(value = "当前登陆人orgId", required = true, example = "999999001")
    private String currentOrgId;
}
