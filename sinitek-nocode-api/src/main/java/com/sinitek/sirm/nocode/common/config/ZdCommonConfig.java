package com.sinitek.sirm.nocode.common.config;

import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2025.0717
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Data
public class ZdCommonConfig {

    @Value("${nocode.app.key:}")
    private String appKey;

    @Value("${nocode.app.name:智搭}")
    private String appName;

    /**
     * 平台管理员的roleId
     */
    @Value("${nocode.platform.admin-role-id:0}")
    private String platformRoleId;


    @Value("${server.servlet.context-path:}")
    private String contextPath;

    public String getAppKey() {
        return "".equals(appKey) ? ZdCommonConstant.APP_KEY : appKey;
    }

    /**
     * 将给定的URL路径拼接上应用的上下文路径，并确保返回的URL以"/"开头。
     *
     * @param url 原始URL路径，不能为空
     * @return 拼接上下文路径后的完整URL路径
     */
    public String withContextPath(String url) {
        url = contextPath + url;
        if (!url.startsWith("/")) {
            url = "/" + url;
        }
        return url;
    }

}
