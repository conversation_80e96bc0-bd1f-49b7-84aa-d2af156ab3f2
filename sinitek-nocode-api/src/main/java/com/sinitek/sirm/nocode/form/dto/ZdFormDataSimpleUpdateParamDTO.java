package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0819
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表单数据简易更新参数")
@Data
public class ZdFormDataSimpleUpdateParamDTO extends AbstractZdFormDataFindBaseParamDTO {

    @ApiModelProperty(value = "更新模式", required = true)
    @NotNull(message = "更新模式不能为空")
    private Integer updateMode;

    @NotEmpty(message = "数据不能为空")
    @ApiModelProperty("数据")
    private Map<String, Object> data;

}
