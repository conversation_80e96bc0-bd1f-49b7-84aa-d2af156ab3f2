package com.sinitek.sirm.nocode.common.support.swagger;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0530
 * @since 1.0.0-SNAPSHOT
 */
@SuppressWarnings("java:S1452")
public interface SwaggerEnumMaker {
    List<? extends BaseEnum<?>> findEnums(ApiEnumProperty apiEnumProperty);
}
