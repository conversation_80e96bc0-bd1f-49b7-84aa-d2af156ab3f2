package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.nocode.page.dto.ZdPagePublishDTO;

/**
 * 页面发布Service接口
 *
 * <AUTHOR>
 * @version 2025.0818
 * @description 针对表【zd_page_publish(页面发布表)】的数据库操作Service
 * @since 1.0.0-SNAPSHOT
 */
public interface IZdPagePublishService {


    /**
     * 保存或更新页面发布信息
     *
     * @param dto 页面发布数据传输对象，包含页面发布相关信息
     * @return 是否保存或更新成功
     * @implNote 若页面发布信息已存在则执行更新操作，否则执行保存操作
     */
    boolean saveOrUpdate(ZdPagePublishDTO dto);


    /**
     * 获取页面自定义URL配置
     *
     * @param pageCode 页面编码
     * @return 自定义URL配置信息
     * @implNote 返回的ZdPageCustomUrlDTO包含完整URL配置数据
     */
    ZdPagePublishDTO getByPageCode(String pageCode);


}
