package com.sinitek.sirm.nocode.common.web;

import com.sinitek.sirm.nocode.common.support.thread.ContextThreadCleaner;
import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 2025.0507
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class RequestContext implements Closeable {
    private static final ThreadLocal<RequestContext> REQUEST_CONTEXT = new ThreadLocal<>();
    private final Map<String, Object> customizeDataMap = new HashMap<>();

    /**
     * appCodeKey
     */
    private static final String APP_CODE = "_appCode";
    public static final String ACCESSTOKEN = "accesstoken";

    /**
     * 用户标识token
     */
    public static final String USER_FLAG_TOKEN = "userFlagToken";
    public static final String AUTHORIZATION_KEY = "Authorization";
    public static final String COOKIE_KEY = "Cookie";
    public static final String CONTENT_TYPE = "Content-Type";


    private RequestContext() {
    }

    public static RequestContext begin() {
        RequestContext requestContext = new RequestContext();
        REQUEST_CONTEXT.set(requestContext);
        return requestContext;
    }


    public static void init(String appCode) {
        getCustomizeDataMap().put(APP_CODE, appCode);

    }

    public static void end() {
        REQUEST_CONTEXT.remove();
    }

    public static Map<String, Object> getCustomizeDataMap() {
        Map<String, Object> customizeDataMap = new HashMap<>();
        RequestContext rc = REQUEST_CONTEXT.get();
        if (rc != null) {
            return rc.customizeDataMap;
        }
        return customizeDataMap;
    }

    public static void remove(String... keys) {
        Map<String, Object> map = getCustomizeDataMap();
        for (String key : keys) {
            map.remove(key);
        }
    }

    public static void clear() {
        getCustomizeDataMap().clear();
    }

    public static String getAppCode() {
        return (String) getCustomizeDataMap().get(APP_CODE);
    }

    public static String getAccessToken() {
        return (String) getCustomizeDataMap().get(ACCESSTOKEN);
    }

    public static void setAccessToken(String accessToken) {
        getCustomizeDataMap().put(ACCESSTOKEN, accessToken);
    }

    static {
        // 静态注入清除者
        ContextThreadCleaner.register(RequestContext::end);
    }

    @Override
    public void close() throws IOException {
        end();
    }

    public static void execute(Consumer<RequestContext> consumer) {
        try (RequestContext requestContext = begin()) {
            consumer.accept(requestContext);
        } catch (Exception e) {
            log.error("RequestContext execute error:{}", e.getMessage(), e);
        }
    }
}
