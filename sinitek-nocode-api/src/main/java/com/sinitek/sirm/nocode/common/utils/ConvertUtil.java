package com.sinitek.sirm.nocode.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@SuppressWarnings({"unchecked"})
public class ConvertUtil {
    private ConvertUtil() {
    }

    public static <T, E extends T> IPage<T> page(Page<E> page) {
        return (IPage<T>) page;
    }

    /**
     * page 转换器
     *
     * @param page   page
     * @param mapper mapper
     * @param <T>    泛型t
     * @param <E>    泛型e
     * @return 转换后的page
     */
    public static <T, E> IPage<T> pageCopy(IPage<E> page, Function<List<E>, List<T>> mapper) {
        IPage<T> newPage = new Page<>();
        newPage.setPages(page.getPages());
        newPage.setCurrent(page.getCurrent());
        newPage.setSize(page.getSize());
        newPage.setTotal(page.getTotal());
        newPage.setRecords(mapper.apply(page.getRecords()));
        return newPage;
    }

    /**
     * list 转换器
     *
     * @param list list
     * @param <A>  泛型a
     * @param <B>  泛型b
     * @return 转换后的list
     */
    public static <A, B> List<B> list(List<A> list) {
        return (List<B>) list;
    }

    /**
     * @param b   源数据
     * @param <A> 泛型a
     * @param <B> 泛型b
     * @return 转换后的list
     */
    public static <A, B> A convert(B b) {
        return (A) b;
    }

    /**
     * 获取非空的值
     *
     * @param a            a
     * @param defaultValue 默认值
     * @param <A>          泛型a
     * @return 获取非空的值
     */
    public static <A> A nullDefault(A a, A defaultValue) {
        return Objects.isNull(a) ? defaultValue : a;
    }

}
