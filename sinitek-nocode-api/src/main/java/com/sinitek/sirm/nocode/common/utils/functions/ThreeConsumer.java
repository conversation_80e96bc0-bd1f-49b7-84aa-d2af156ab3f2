package com.sinitek.sirm.nocode.common.utils.functions;

/**
 * <AUTHOR>
 * @version 2025.08.20
 * @since 1.0.0-SNAPSHOT
 */
@FunctionalInterface
public interface ThreeConsumer<T, U, R> {
    /**
     * Performs this operation on the given arguments.
     *
     * @param t the first input argument
     * @param u the second input argument
     * @param r the third input argument
     */
    void accept(T t, U u, R r);
}
