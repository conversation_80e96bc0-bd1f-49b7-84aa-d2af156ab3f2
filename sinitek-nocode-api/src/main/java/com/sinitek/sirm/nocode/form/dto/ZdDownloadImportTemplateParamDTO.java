package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-07-30 14:51
 */
@Data
@NoArgsConstructor
@ApiModel(description = "下载导入模板参数")
public class ZdDownloadImportTemplateParamDTO implements FormCodeSupplier {

    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("类型")
    private Integer templateType;
}
