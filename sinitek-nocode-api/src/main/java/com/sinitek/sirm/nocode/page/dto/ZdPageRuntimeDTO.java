package com.sinitek.sirm.nocode.page.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面表
 *
 * @TableName zd_page
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "运行时页面DTO")
public class ZdPageRuntimeDTO extends ZdPageDTO {
    @ApiModelProperty(value = "是否有提交权限")
    private Boolean hasSubmitRight;
    @ApiModelProperty(value = "是否有数据管理权限")
    private Boolean hasDataRight;
}
