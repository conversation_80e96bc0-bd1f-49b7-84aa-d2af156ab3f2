package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.sinitek.sirm.common.utils.GlobalConstant;
import org.apache.commons.lang3.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @version 2025.0515
 * @since 1.0.0-SNAPSHOT
 */

public class ZdDateUtil {
    private static final int WEEK_DATES = 6;

    /**
     * 年: 'YYYY',
     * '年-月': 'YYYY-MM',
     * '年-月-日': 'YYYY-MM-DD',
     * '年-月-日 时:分': 'YYYY-MM-DD HH:mm',
     * '年-月-日 时:分:秒': 'YYYY-MM-DD HH:mm:ss',
     */
    public static final String YYYY = "yyyy";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private ZdDateUtil() {
    }

    /**
     * 获取指定日期的开始时间和结束时间
     *
     * @param date 指定日期
     * @return Pair<LocalDateTime, LocalDateTime>
     */
    public static Pair<LocalDateTime, LocalDateTime> day(LocalDate date) {
        if (Objects.isNull(date)) {
            date = LocalDate.now();
        }
        // 当天开始时间（00:00:00）
        LocalDateTime startOfDay = date.atStartOfDay();
        // 当天结束时间（23:59:59.999999999）
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
        return Pair.of(startOfDay, endOfDay);
    }

    /**
     * 获取指定日期的当周开始时间和结束时间
     *
     * @param date 指定日期
     * @return Pair<LocalDateTime, LocalDateTime>
     */
    public static Pair<LocalDateTime, LocalDateTime> week(LocalDate date) {
        if (Objects.isNull(date)) {
            date = LocalDate.now();
        }
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        DayOfWeek firstDayOfWeek = weekFields.getFirstDayOfWeek();

        // 计算本周开始时间（当天的 00:00:00）
        LocalDate startOfWeekDate = date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek));
        LocalDateTime startOfWeek = startOfWeekDate.atStartOfDay();

        // 计算本周结束时间（最后一天的 23:59:59.999999999）
        LocalDate endOfWeekDate = startOfWeekDate.plusDays(WEEK_DATES);
        LocalDateTime endOfWeek = endOfWeekDate.atTime(LocalTime.MAX);
        return Pair.of(startOfWeek, endOfWeek);
    }

    /**
     * 获取指定日期的当月开始时间和结束时间
     *
     * @param date 指定日期
     * @return Pair<LocalDateTime, LocalDateTime>
     */
    public static Pair<LocalDateTime, LocalDateTime> month(LocalDate date) {
        if (Objects.isNull(date)) {
            date = LocalDate.now();
        }

        // 当月开始时间（首日 00:00:00）
        LocalDateTime startOfMonth = date
                .with(TemporalAdjusters.firstDayOfMonth())
                .atStartOfDay();
        // 当月结束时间（最后一日 23:59:59.999999999）
        LocalDateTime endOfMonth = date
                .with(TemporalAdjusters.lastDayOfMonth())
                .atTime(LocalTime.MAX);
        return Pair.of(startOfMonth, endOfMonth);
    }


    /**
     * 时间类型转化
     *
     * @param localDateTime 时间
     * @return Date
     */
    public static Date toDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 时间类型转化
     *
     * @param localDate 时间
     * @return Date
     */
    public static Date toDate(LocalDate localDate) {
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

    public static LocalDate toLocalDate(Date date) {
        ZoneId zone = ZoneId.systemDefault();
        return date.toInstant()       // Date → Instant
                .atZone(zone)       // Instant → ZonedDateTime（附加时区）
                .toLocalDate();     // 提取日期部分
    }

    /**
     * 时间类型转化
     *
     * @param date 日期
     * @return 字符串日期
     */

    public static String format(LocalDate date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return dateTimeFormatter.format(date);
    }

    /**
     * 时间类型转化
     *
     * @param date 日期
     * @return 字符串日期
     */

    public static LocalDate format(String date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(date, dateTimeFormatter);
    }

    /**
     * 因为前端不愿意修改，针对前端封装后端再封装
     * <p>
     * 前端代码位置: sinitek-lowcode-frontend\packages\sinitek-lowcode-form\src\zd\common\enum.js
     * <p>
     * 年: 'YYYY',
     * '年-月': 'YYYY-MM',
     * '年-月-日': 'YYYY-MM-DD',
     * '年-月-日 时:分': 'YYYY-MM-DD HH:mm',
     * '年-月-日 时:分:秒': 'YYYY-MM-DD HH:mm:ss',
     */
    public static String getFormatPattern(String frontendFormatPattern) {
        return getFormatPattern(frontendFormatPattern, GlobalConstant.TIME_FORMAT_TEN);
    }

    public static String getFormatPattern(String frontendFormatPattern, String defaultValue) {
        String format;
        switch (frontendFormatPattern) {
            case YYYY: {
                format = "yyyy";
                break;
            }
            case YYYY_MM: {
                format = YYYY_MM;
                break;
            }
            case YYYY_MM_DD: {
                format = GlobalConstant.TIME_FORMAT_TEN;
                break;
            }
            case YYYY_MM_DD_HH_MM: {
                format = YYYY_MM_DD_HH_MM;
                break;
            }
            case YYYY_MM_DD_HH_MM_SS: {
                format = GlobalConstant.TIME_FORMAT_THIRTEEN;
                break;
            }
            default: {
                if (StringUtils.isNotBlank(defaultValue)) {
                    format = defaultValue;
                } else {
                    format = null;
                }
                break;
            }
        }
        return format;
    }

    public static String formatTimestamp(Long timestamp, String frontendFormatPattern) {
        if (Objects.nonNull(timestamp)
                && StringUtils.isNotBlank(frontendFormatPattern)) {
            String format = getFormatPattern(frontendFormatPattern);
            Date date = new Date(timestamp);
            return DateUtil.format(date, format);
        } else {
            return null;
        }
    }

    public static Long getTimestamp(String dateStr, String frontendFormatPattern) {
        if (StringUtils.isNotBlank(dateStr)
                && StringUtils.isNotBlank(frontendFormatPattern)) {
            DateTime parse = DateUtil.parse(dateStr.trim(),
                    getFormatPattern(frontendFormatPattern));
            return parse.getTime();
        } else {
            return null;
        }
    }
}
