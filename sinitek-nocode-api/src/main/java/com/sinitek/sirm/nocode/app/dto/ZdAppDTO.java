package com.sinitek.sirm.nocode.app.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.app.support.AppCodeSupplier;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 应用数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应用DTO")
public class ZdAppDTO extends ZdIdDTO implements AppCodeSupplier {
    private static final long serialVersionUID = -6680770022308971988L;
    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", example = "销假应用")
    private String name;

    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    @Pattern(regexp = "^[a-zA-Z][\\w-]*$", message = "应用编码必须以字母开头，由英文字母、数字、下划线（_）、连字符（-）组成")
    @ApiModelProperty(name = "code", value = "应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
    private String code;

    /**
     * 密钥
     */
    @JsonIgnore
    @ApiModelProperty("密钥")
    private String appSecret;

    /**
     * 应用描述
     */
    @ApiModelProperty("应用描述")
    private String description;

    /**
     * 应用图标类型
     */
    @ApiModelProperty("应用图标类型")
    private String iconfont;

    /**
     * 应用背景
     */
    @ApiModelProperty("应用背景")
    private String background;

    /**
     * 主题颜色
     */
    @ApiModelProperty("主题颜色")
    private String themeColor;

    /**
     * 状态，0:未启用，1:已经启用
     */
    @ApiModelProperty("状态，0:未启用，1:已经启用")
    private StatusEnum status;

    /**
     * 页面地址
     */
    @ApiModelProperty(value = "页面地址", example = "abc1000")
    private String url;
}
