package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用管理员
 *
 * @TableName zd_app_manager
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应用管理员DTO")
public class ZdAppManagerDTO extends ZdIdDTO {
    private static final long serialVersionUID = -4618190385082965453L;
    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码", example = "abc1000")
    private String appCode;

    /**
     * orgId
     */
    @ApiModelProperty("组织id")
    private String orgId;
}
