package com.sinitek.sirm.nocode.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.SerializationException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2025.0801
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class ObjectSerializerUtil {
    private ObjectSerializerUtil() {
    }

    public static final int BYTE_ARRAY_OUTPUT_STREAM_SIZE = 128;

    public static byte[] serialize(Object object) {
        byte[] result = new byte[0];
        if (object == null) {
            return result;
        } else {
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream(BYTE_ARRAY_OUTPUT_STREAM_SIZE);
            if (!(object instanceof Serializable)) {
                throw new SerializationException("requires a Serializable payload but received an object of type [" + object.getClass().getName() + "]");
            } else {
                try {
                    ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteStream);
                    objectOutputStream.writeObject(object);
                    objectOutputStream.flush();
                    result = byteStream.toByteArray();
                    return result;
                } catch (IOException e) {
                    log.error("serialize error:{}", e.getMessage(), e);
                    throw new SerializationException("serialize error, object=" + object, e);
                }
            }
        }
    }

    public static Object deserialize(byte[] bytes) {
        Object result = null;
        if (bytes != null && bytes.length != 0) {
            try {
                ByteArrayInputStream byteStream = new ByteArrayInputStream(bytes);
                ObjectInputStream objectInputStream = new ObjectInputStream(byteStream);
                result = objectInputStream.readObject();
                return result;
            } catch (IOException | ClassNotFoundException e) {
                log.error("deserialize error:{}", e.getMessage(), e);
                throw new SerializationException("deserialize error", e);
            }
        } else {
            return result;
        }
    }
}
