package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormConfigDTO;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;

/**
 * <AUTHOR>
 * @version 2025-03-21 18:28:16
 * @description 针对表【zd_page_form_config(表单配置表)】的数据库操作Service
 */
public interface IZdPageFormConfigService {

    /**
     * 根据表单编码获取表名
     *
     * @param formCode 表单编码
     * @return 表名
     */
    String getTableNameByFormCode(String formCode);

    /**
     * 通过表单编码获取 表单配置
     *
     * @param formCode 表单编码
     * @return 表单配置
     */
    ZdPageFormConfigDTO getByFormCode(String formCode);

    /**
     * 创建表单配置
     *
     * @param appCode      应用编码
     * @param formCode     表单编码
     * @param pageTypeEnum 页面类型
     * @param name         表单名称
     */

    void createFormConfig(String appCode, String formCode, PageTypeEnum pageTypeEnum, String name);

    /**
     * 获取空闲表的数量
     *
     * @return 空闲表的数量
     */
    int getIdleTableLength();

}
