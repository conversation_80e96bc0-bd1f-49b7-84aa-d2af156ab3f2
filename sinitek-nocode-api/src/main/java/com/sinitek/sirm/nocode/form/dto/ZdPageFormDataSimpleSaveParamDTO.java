package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.base.BaseAuditEntityDTO;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单数据简易新增参数DTO")

public class ZdPageFormDataSimpleSaveParamDTO extends BaseAuditEntityDTO implements
    FormCodeSupplier {

    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty(value = "表单编码", required = true, example = "page_ca54fa8cfaab4aa58362421e542142d2")
    private String formCode;

    /**
     * 数据
     */
    @NotEmpty(message = "表单数据不能为空")
    @ApiModelProperty("表单数据")
    private Map<String, Object> data;

}
