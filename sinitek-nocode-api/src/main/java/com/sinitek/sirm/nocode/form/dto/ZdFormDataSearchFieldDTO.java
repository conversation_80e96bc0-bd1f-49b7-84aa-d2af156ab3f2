package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.VariableTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表单数据搜索字段参数")
@Data
public class ZdFormDataSearchFieldDTO extends ZdFormDataSearchBaseFieldDTO {
    /**
     * 变量类型
     */
    @ApiEnumProperty
    private VariableTypeEnum variableType;
    /**
     * 操作符
     */
    @NotNull(message = "操作符不能为空")
    @ApiEnumProperty(required = true)
    private OperatorEnum operator;

}
