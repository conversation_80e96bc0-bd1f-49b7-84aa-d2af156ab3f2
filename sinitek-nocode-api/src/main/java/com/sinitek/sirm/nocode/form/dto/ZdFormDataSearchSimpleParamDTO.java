package com.sinitek.sirm.nocode.form.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.nocode.common.dto.SimpleSearchOrderItemDTO;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单数据简易搜索参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdFormDataSearchSimpleParamDTO extends PageDataParam implements FormCodeSupplier {

    @ApiModelProperty(value = "表单编码", required = true)
    @NotBlank(message = "表单编码不能为空")
    private String formCode;

    @ApiModelProperty("搜索字段,支持数据库本身字段查询(自带字段，有四个(字段描述，字段名称，值类型):" +
        "<br/>主键，_FDC_id，int" +
        "<br/>创建人id，_FDC_creator_id，text" +
        "<br/>创建时间，_FDC_createtimestamp，timestamp" +
        "<br/>修改时间，_FDC_updatetimestamp，timestamp"
    )
    private List<ZdFormDataSearchFieldDTO> searchField;


    @ApiModelProperty("排序字段,数组，支持多个字段排序")
    private List<SimpleSearchOrderItemDTO> orderItemList;

    @JsonIgnore
    @ApiModelProperty(value = "当前登陆人orgId", required = true, example = "999999001")
    private String currentOrgId;

    @ApiModelProperty(value = "需要查询的列")
    private List<String> columnList;
}
