package com.sinitek.sirm.nocode.common.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.common.utils.functions.ThreeConsumer;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 2025.0820
 * @since 1.0.0-SNAPSHOT
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ZdTreeUtil {
    public interface ChildrenProvider<T> {
        Collection<T> getChildren();
    }

    /**
     * 子类数据扁平化
     *
     * @param list      原始数据
     * @param childFunc 子类提供类
     * @param <T>       泛型
     * @return 所有的数据
     */
    public static <T> List<T> flatChild(Collection<T> list, Function<T, Collection<T>> childFunc) {
        List<T> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            result.addAll(list);
            list.forEach(t -> {
                Collection<T> children = childFunc.apply(t);
                if (CollectionUtils.isNotEmpty(children)) {
                    result.addAll(flatChild(children, childFunc));
                }
            });
        }
        return result;
    }

    /**
     * 子类数据扁平化
     *
     * @param list      原始数据
     * @param childFunc 子类提供类
     * @param consumer  消费者
     * @param <T>       泛型
     */
    public static <T> void flatChildAction(Collection<T> list, Function<T, Collection<T>> childFunc, Consumer<T> consumer) {
        flatChildAction(list, childFunc, (a, b) -> consumer.accept(a));
    }

    /**
     * 子类数据扁平化
     *
     * @param list      原始数据
     * @param childFunc 子类提供类
     * @param consumer  消费者
     * @param <T>       泛型
     */
    public static <T> void flatChildAction(Collection<T> list, Function<T, Collection<T>> childFunc, BiConsumer<T, AtomicBoolean> consumer) {
        flatChildAction(list, childFunc, (a, b, c) -> consumer.accept(a, c), new AtomicBoolean());
    }

    /**
     * 子类数据扁平化
     *
     * @param list      原始数据
     * @param childFunc 子类提供类
     * @param consumer  消费者
     * @param <T>       泛型
     */
    public static <T> void flatChildAction(Collection<T> list, Function<T, Collection<T>> childFunc, ThreeConsumer<T, Iterator<T>, AtomicBoolean> consumer) {
        flatChildAction(list, childFunc, consumer, new AtomicBoolean());
    }

    /**
     * 子类数据扁平化
     *
     * @param list      原始数据
     * @param childFunc 子类提供类
     * @param consumer  消费者
     * @param breakFlag 停止标志
     * @param <T>       泛型
     */
    private static <T> void flatChildAction(Collection<T> list, Function<T, Collection<T>> childFunc, ThreeConsumer<T, Iterator<T>, AtomicBoolean> consumer, AtomicBoolean breakFlag) {
        if (CollectionUtils.isNotEmpty(list)) {
            Iterator<T> iterator = list.iterator();
            while (iterator.hasNext()) {
                T t = iterator.next();
                consumer.accept(t, iterator, breakFlag);
                if (breakFlag.get()) {
                    break;
                }
                Collection<T> children = childFunc.apply(t);
                if (CollectionUtils.isNotEmpty(children)) {
                    flatChildAction(children, childFunc, consumer, breakFlag);
                }
            }
        }
    }
}
