package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-22 13:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("智搭提交容器字段")
public class ZdSubmitFieldContainerDTO extends AbstractZdSubmitFieldBaseDTO {

    @ApiModelProperty("子组件")
    private List<AbstractZdSubmitFieldBaseDTO> children;

}
