package com.sinitek.sirm.nocode.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sinitek.sirm.framework.frontend.support.OrderNameDeserializer;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.form.enumerate.ValueTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0519
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "排序对象")
public class SimpleSearchOrderItemDTO {

    @NotBlank(message = "字段名不能为空")
    @ApiModelProperty(value = "排序属性名称", required = true)
    @JsonDeserialize(
        using = OrderNameDeserializer.class
    )
    private String orderName;

    @NotBlank(message = "排序类型不能为空")
    @ApiModelProperty(value = "排序类型，asc-升序，desc-降序", required = true)
    private String orderType;

    @NotNull(message = "值类型不能为空")
    @ApiEnumProperty(required = true)
    private ValueTypeEnum valueType;
}
