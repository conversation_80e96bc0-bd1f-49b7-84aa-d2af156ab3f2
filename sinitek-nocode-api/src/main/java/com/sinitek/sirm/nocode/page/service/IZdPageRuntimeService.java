package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.nocode.page.dto.ZdPageRuntimeDTO;

import java.util.List;

/**
 * 运行时页面服务
 *
 * <AUTHOR>
 * @version 2025.0820
 * @since 1.0.0-SNAPSHOT
 */

public interface IZdPageRuntimeService {
    /**
     * 获取已经发布的页面列表
     *
     * @param appCode 应用编码
     * @param orgId
     * @return 页面列表
     */
    List<ZdPageRuntimeDTO> publishedListTree(String appCode, String orgId);

    /**
     * 运行时获取第一个默认表单编码
     *
     * @param appCode 应用编码
     * @return 表单编码
     */
    String getFirstDefaultFormCode(String appCode, String orgId);
}
