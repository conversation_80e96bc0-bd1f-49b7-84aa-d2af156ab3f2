package com.sinitek.sirm.nocode.form.config;

import com.sinitek.sirm.nocode.form.constant.ZdAiWorkflowConstant;
import com.sinitek.sirm.nocode.form.interceptor.AiWorkflowFormDataInterceptor;
import javax.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * AI工作流表单数据Web配置
 * 用于注册AI工作流表单数据拦截器
 *
 * <AUTHOR>
 * @version 2025.0822
 * @since 1.0.0-SNAPSHOT
 */
@Configuration
public class AiWorkflowFormDataWebConfig implements WebMvcConfigurer {
    // AI工作流表单数据拦截器执行顺序
    private static final int AI_WORKFLOW_FORM_DATA_INTERCEPTOR_ORDER = 10;

    @Resource
    private AiWorkflowFormDataInterceptor aiWorkflowFormDataInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册AI工作流表单数据拦截器
        registry.addInterceptor(aiWorkflowFormDataInterceptor)
            .addPathPatterns(String.format("%s/**", ZdAiWorkflowConstant.DATA_OP_BASE_URL))
            .order(AI_WORKFLOW_FORM_DATA_INTERCEPTOR_ORDER); // 设置拦截器执行顺序，数字越小优先级越高
    }
}