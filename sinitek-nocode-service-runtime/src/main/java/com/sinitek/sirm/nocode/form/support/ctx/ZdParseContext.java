package com.sinitek.sirm.nocode.form.support.ctx;

import com.sinitek.sirm.org.dto.OrgObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-07-24 09:24
 */
@Data
@ApiModel("智搭解析上下文")
public class ZdParseContext {

    protected static final int DEFAULT_INIT_SIZE = 10000;

    @ApiModelProperty("员工姓名与orgId映射")
    private Map<String, List<OrgObjectDTO>> orgNameAndOrgIdMap;

    @ApiModelProperty("部门名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> deptNameAndOrgIdMap;

    @ApiModelProperty("岗位名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> positionNameAndOrgIdMap;

    @ApiModelProperty("小组名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> teamNameAndOrgIdMap;

    @ApiModelProperty("角色名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> roleNameAndOrgIdMap;

    @ApiModelProperty("组织ID与组织对象映射")
    private Map<String, OrgObjectDTO> orgIdAndOrgObjectMap;

    public ZdParseContext() {
        this.orgIdAndOrgObjectMap = new HashMap<>(DEFAULT_INIT_SIZE);
    }

    public void putOrgIdAndOrgObjectMap(String orgId, OrgObjectDTO orgObject) {
        this.orgIdAndOrgObjectMap.put(orgId, orgObject);
    }
}
