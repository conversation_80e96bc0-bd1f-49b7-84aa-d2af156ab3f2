package com.sinitek.sirm.nocode.form.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.form.aspect.ZdAuth;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataDeleteByConditionParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchSimpleParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSimpleUpdateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataSimpleSaveParamDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 2025.0407
 * @description 表单数据管理
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/zhida-open-api/nocode/ai-work-flow/form/data", tags = "ai工作流表单数据管理")
@RequestMapping("/frontend/api/zhida-open-api/nocode/ai-work-flow/form/data")
public class ZdAiWorkflowFormDataController {

    @Resource
    private IZdPageFormDataService pageFormDataService;

    @ZdAuth(operationAuthType = OperationAuthEnum.SUBMIT, checkStatus = true)
    @ApiOperation(value = "新增")
    @PostMapping("/simple-save")
    public RequestResult<Long> simpleSave(
        @RequestBody @ApiParam(value = "表单数据保存或者修改", name = "表单数据保存或者修改") @Valid ZdPageFormDataSimpleSaveParamDTO param) {
        param.setCreatorId(CurrentUserFactory.getOrgId());
        return new RequestResult<>(pageFormDataService.simpleSaveEffective(param));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.DELETE, checkStatus = true)
    @ApiOperation(value = "删除")
    @PostMapping("/simple-delete")
    public RequestResult<Integer> simpleDelete(
        @RequestBody @ApiParam(value = "删除参数") @Valid ZdFormDataDeleteByConditionParamDTO param) {
        return new RequestResult<>(pageFormDataService.deleteEffectiveByCondition(param));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.DELETE, checkStatus = true)
    @ApiOperation(value = "修改")
    @PostMapping("/simple-update")
    public RequestResult<Integer> simpleUpdate(
        @RequestBody @ApiParam(value = "修改参数") @Valid ZdFormDataSimpleUpdateParamDTO param) {
        param.setCurrentOrgId(CurrentUserFactory.getOrgId());
        return new RequestResult<>(
            pageFormDataService.updateEffectiveByCondition(param));
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @ApiOperation(value = "查询")
    @PostMapping("/simple-search")
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    public TableResult<Map<String, Object>> search(
        @RequestBody @ApiParam(value = "表单数据查询条件", name = "表单数据查询条件") @Valid ZdFormDataSearchSimpleParamDTO param) {
        param.setCurrentOrgId(CurrentUserFactory.getOrgId());
        return pageFormDataService.simpleSearchEffectiveData(param);
    }
}
