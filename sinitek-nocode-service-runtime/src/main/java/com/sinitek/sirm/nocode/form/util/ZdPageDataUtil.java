package com.sinitek.sirm.nocode.form.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.constant.ComponentFieldConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataExtraFieldConstant;
import com.sinitek.sirm.nocode.form.dto.AbstractZdSubmitFieldBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormPageDataDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.dto.ZdParseResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdSubmitFieldContainerDTO;
import com.sinitek.sirm.nocode.form.dto.ZdSubmitFieldDTO;
import com.sinitek.sirm.nocode.form.enumerate.DownloadImportTempalteTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 2025-07-16 15:30
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdPageDataUtil {

    private static final int DEFAULT_INITIAL_CAPACITY = 200;

    public static ZdParseResultDTO parsePageData(Map<String, Object> pageDataMap) {
        return parsePageData(pageDataMap, DownloadImportTempalteTypeEnum.ALL_FIELD);
    }

    public static ZdParseResultDTO parsePageData(Map<String, Object> pageDataMap,
        DownloadImportTempalteTypeEnum type) {
        List<ZdImportFormHeaderFieldDTO> headFields = new LinkedList<>();
        List<ZdImportFormHeaderFieldDTO> formFields = new LinkedList<>();

        List<AbstractZdSubmitFieldBaseDTO> submitFields = new LinkedList<>();

        // key: 组件ref,value: 组件信息
        Map<String, ZdPageComponentDTO> refAndComponentMap = new HashMap<>(
            DEFAULT_INITIAL_CAPACITY);

        // key: 父组件ref,value: 父组件提交字段
        Map<String, AbstractZdSubmitFieldBaseDTO> parentRefAndSubmitFieldMap = new HashMap<>(
            DEFAULT_INITIAL_CAPACITY);

        // key: 父组件ref,value: 是否追加子表单id标识
        Map<String, Boolean> parentRefAndAppendChildIdFlagMap = new HashMap<>(
            DEFAULT_INITIAL_CAPACITY);

        boolean isAllField = Objects.equals(type, DownloadImportTempalteTypeEnum.ALL_FIELD);

        ZdFormPageDataDTO.walk(pageDataMap, (parent, child) -> {
            String ref = MapUtils.getString(child, ComponentFieldConstant.REF);
            String componentName = MapUtils.getString(child,
                ComponentFieldConstant.COMPONENT_NAME);
            Map<String, Object> props = getProps(child);
            String label = MapUtils.getString(props, ComponentFieldConstant.LABEL_IN_PROPS);

            ZdPageComponentDTO component = ZdPageComponentDTO.builder()
                .ref(ref)
                .label(label)
                .componentName(componentName)
                .props(props)
                .children(new LinkedList<>())
                .build();

            List<Map<String, Object>> children = findChildren(child);
            boolean hasChildren = CollUtil.isNotEmpty(children);

            String parentLabel = null;
            String parentRef = null;
            boolean isChildForm = false;

            boolean isPageOrForm = (
                Objects.equals(componentName, PageDataComponentTypeEnum.ZD_PAGE.getValue())
                    || Objects.equals(componentName, PageDataComponentTypeEnum.ZD_FORM.getValue()));

            if (isPageOrForm) {
                return;
            }

            if (Objects.nonNull(parent)) {
                String parentComponentName = MapUtils.getString(parent,
                    ComponentFieldConstant.COMPONENT_NAME);
                boolean isParentPageOrForm = (
                    Objects.equals(parentComponentName,
                        PageDataComponentTypeEnum.ZD_PAGE.getValue())
                        || Objects.equals(parentComponentName,
                        PageDataComponentTypeEnum.ZD_FORM.getValue()));

                // 有父组件
                if (!isParentPageOrForm) {
                    parentRef = MapUtils.getString(parent, ComponentFieldConstant.REF);
                    Map<String, Object> parentProps = getProps(parent);
                    parentLabel = MapUtils.getString(parentProps,
                        ComponentFieldConstant.LABEL_IN_PROPS);
                }

                ZdPageComponentDTO parentComponent = MapUtils.getObject(refAndComponentMap,
                    parentRef);

                if (Objects.nonNull(parentComponent)) {
                    List<ZdPageComponentDTO> childrenInParent = parentComponent.getChildren();

                    // 父组件添加子组件
                    childrenInParent.add(component);
                }

                ZdSubmitFieldDTO submitField = ZdSubmitFieldDTO.builder()
                    .ref(ref)
                    .label(label)
                    .componentName(componentName)
                    .value(null)
                    .build();

                // 父组件提交字段添加子组件
                ZdSubmitFieldContainerDTO parentSubmitField = (ZdSubmitFieldContainerDTO) parentRefAndSubmitFieldMap.get(
                    parentRef);

                if (Objects.nonNull(parentSubmitField)) {
                    List<AbstractZdSubmitFieldBaseDTO> childrenInSubmitField = parentSubmitField.getChildren();
                    childrenInSubmitField.add(submitField);
                } else {
                    submitFields.add(submitField);
                }

                // 父组件追加子表单id标识
                Boolean isAppended = parentRefAndAppendChildIdFlagMap.get(parentRef);
                if (Objects.isNull(isAppended)
                    && !isParentPageOrForm
                    && isAllField) {
                    parentRefAndAppendChildIdFlagMap.put(parentRef, true);
                    formFields.add(ZdImportFormHeaderFieldDTO.builder()
                        .ref(FormDataExtraFieldConstant.ID_FIELD_KEY)
                        .componentName(null)
                        .label(FormDataExtraFieldConstant.ID_FIELD_NAME)
                        .formatter(null)
                        .options(null)
                        .parentLabel(parentLabel)
                        .parentRef(parentRef)
                        .childFormItemFlag(true)
                        .build());
                }
            } else {
                // 没有父组件
                AbstractZdSubmitFieldBaseDTO realSubmitField;
                if (hasChildren) {
                    realSubmitField = ZdSubmitFieldContainerDTO.builder()
                        .ref(ref)
                        .label(label)
                        .componentName(componentName)
                        .value(null)
                        .children(new LinkedList<>())
                        .build();
                    parentRefAndSubmitFieldMap.put(ref, realSubmitField);
                } else {
                    realSubmitField = ZdSubmitFieldDTO.builder()
                        .ref(ref)
                        .label(label)
                        .componentName(componentName)
                        .value(null)
                        .build();
                }

                submitFields.add(realSubmitField);
            }

            if (!ZdComponentUtil.isContainComponent(componentName)
                && !ZdComponentUtil.isChildFormComponent(componentName)) {
                String formatter = MapUtils.getString(props,
                    ComponentFieldConstant.FORMATTER_IN_PROPS);
                // 选项
                List<Map<String, Object>> options = (List<Map<String, Object>>) MapUtils.getObject(
                    props,
                    ComponentFieldConstant.OPTIONS_IN_PROPS);
                formFields.add(ZdImportFormHeaderFieldDTO.builder()
                    .ref(ref)
                    .componentName(componentName)
                    .label(label)
                    .formatter(formatter)
                    .options(options)
                    .parentLabel(parentLabel)
                    .parentRef(parentRef)
                    .childFormItemFlag(isChildForm)
                    .build());
            }

            if (!isPageOrForm) {
                refAndComponentMap.put(ref, component);
            }
        });

        if (isAllField) {
            headFields.add(ZdImportFormHeaderFieldDTO.builder()
                .ref(FormDataExtraFieldConstant.ID_FIELD_KEY)
                .componentName(null)
                .label(FormDataExtraFieldConstant.ID_FIELD_NAME)
                .formatter(null)
                .options(null)
                .parentLabel(null)
                .parentRef(null)
                .childFormItemFlag(false)
                .build());
        }

        // 表单字段
        headFields.addAll(formFields);

        if (isAllField) {
            // 提交人
            headFields.add(ZdImportFormHeaderFieldDTO.builder()
                .ref(FormDataExtraFieldConstant.CREATOR_FIELD_KEY)
                .componentName(null)
                .label(FormDataExtraFieldConstant.CREATOR_FIELD_NAME)
                .formatter(null)
                .options(null)
                .parentLabel(null)
                .parentRef(null)
                .childFormItemFlag(false)
                .build());
            // 创建时间
            headFields.add(ZdImportFormHeaderFieldDTO.builder()
                .ref(FormDataExtraFieldConstant.CREATE_TIMESTAMP_FIELD_KEY)
                .componentName(null)
                .label(FormDataExtraFieldConstant.CREATE_TIMESTAMP_FIELD_NAME)
                .formatter(null)
                .options(null)
                .parentLabel(null)
                .parentRef(null)
                .childFormItemFlag(false)
                .build());
            // 修改时间
            headFields.add(ZdImportFormHeaderFieldDTO.builder()
                .ref(FormDataExtraFieldConstant.UPDATE_TIMESTAMP_FIELD_KEY)
                .componentName(null)
                .label(FormDataExtraFieldConstant.UPDATE_TIMESTAMP_FIELD_NAME)
                .formatter(null)
                .options(null)
                .parentLabel(null)
                .parentRef(null)
                .childFormItemFlag(false)
                .build());
        }

        return ZdParseResultDTO.builder()
            .refAndComponentMap(refAndComponentMap)
            .submitFields(submitFields)
            .headFields(headFields)
            .schema(pageDataMap)
            .build();
    }

    private static Map<String, Object> getProps(Map<String, Object> componentData) {
        return (Map<String, Object>) MapUtils.getObject(componentData,
            ComponentFieldConstant.PROPS);
    }

    private static List<Map<String, Object>> findChildren(Map<String, Object> componentData) {
        return (List<Map<String, Object>>) MapUtils.getObject(componentData,
            ComponentFieldConstant.CHILDREN);
    }
}
