package com.sinitek.sirm.nocode.form.interceptor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.cloud.base.support.CurrentUser;
import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.nocode.form.constant.ZdAiWorkflowConstant;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.sinitek.sirm.nocode.common.constant.ZdCommonConstant.ORG_ID_HEADER;

/**
 * AI工作流表单数据拦截器
 * 用于初始化操作人，操作人从请求头的orgid中获取
 *
 * <AUTHOR>
 * @version 2025.0822
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Slf4j
public class AiWorkflowFormDataInterceptor implements HandlerInterceptor {

    @Autowired
    private IOrgService orgService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        String requestUri = request.getRequestURI();
        
        // 只拦截AI工作流表单数据相关的请求
        if (!requestUri.contains(ZdAiWorkflowConstant.DATA_OP_BASE_URL)) {
            return true;
        }
        String orgId = request.getHeader(ORG_ID_HEADER);

        if (StringUtils.isNotBlank(orgId)) {
            // 初始化操作人信息
            try {
                log.debug("AI工作流表单数据请求，从请求头获取到orgId: {}, 请求URI: {}",
                        orgId, requestUri);

                Employee employee = this.orgService.getEmployeeById(orgId);

                if (Objects.nonNull(employee)) {
                    // 创建一个临时的UserDTO对象来设置用户上下文
                    UserDTO user = new UserDTO();
                    user.setOrgid(orgId);
                    user.setOrgname(employee.getEmpName());
                    user.setUserid(employee.getUserId());
                    user.setUsename(employee.getUserName());

                    // 初始化CurrentUser上下文
                    CurrentUser.begin();
                    CurrentUser.setRequest(request);
                    CurrentUser.init(user);

                    log.debug("成功设置AI工作流表单数据请求操作人，orgId: {}", orgId);

                    return true;
                } else {
                    log.warn(
                            "AI工作流表单数据操作请求，从请求头获取到orgId: {}, 但在组织服务中未找到对应的员工信息，请求URI: {}",
                            orgId, requestUri);
                }
            } catch (Exception e) {
                log.error("设置AI工作流操作人失败，orgId: {}, 请求URI: {}", orgId,
                        requestUri, e);
            }
        } else {
            log.warn("AI工作流表单数据请求缺少orgid请求头，请求URI: {}", requestUri);
        }

        log.debug("调用ai工作流操作表单数据接口未传递有效orgId");
        Map<String, String> res = new HashMap<>();
        res.put("result", "010110");
        res.put("message", "缺少操作人信息");
        ObjectMapper mapper = new ObjectMapper();
        String subject = "";
        try {
            subject = mapper.writeValueAsString(res);
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常:{}", e.getMessage(), e);
            subject = "缺少操作人信息";
        }

        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.getWriter().println(subject);

        return false;

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) throws Exception {
        // 清理CurrentUser上下文
        CurrentUser.end();
    }
}
