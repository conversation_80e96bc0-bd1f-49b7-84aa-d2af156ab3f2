package com.sinitek.sirm.nocode.form.support.handler.parse;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseContext;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @date 2025-08-21 09:07
 */
public abstract class AbstractOrgComponentValueParser {

    protected IOrgService orgService;

    public AbstractOrgComponentValueParser(IOrgService orgService) {
        this.orgService = orgService;
    }

    protected List<String> initOrgCache(ZdParseContext ctx, boolean multipleFlag, String rowOrgId) {
        Map<String, OrgObjectDTO> orgIdAndOrgObjectMap = ctx.getOrgIdAndOrgObjectMap();

        List<String> orgIds = new LinkedList<>();
        List<String> notInCacheOrgIds = new LinkedList<>();
        if (multipleFlag) {
            Arrays.stream(rowOrgId.split(",")).forEach(orgIdInner -> {
                orgIds.add(orgIdInner);
                if (!this.isInCache(orgIdAndOrgObjectMap, orgIdInner)) {
                    notInCacheOrgIds.add(orgIdInner);
                }
            });
        } else {
            orgIds.add(rowOrgId);
            if (!this.isInCache(orgIdAndOrgObjectMap, rowOrgId)) {
                notInCacheOrgIds.add(rowOrgId);
            }
        }
        if (CollUtil.isNotEmpty(notInCacheOrgIds)) {
            List<OrgObjectDTO> orgObjectsByOrgIds = this.orgService.findOrgObjectsByOrgIds(
                notInCacheOrgIds);
            orgObjectsByOrgIds.forEach(item -> {
                ctx.putOrgIdAndOrgObjectMap(item.getOrgId(), item);
            });
        }
        return orgIds;
    }

    protected boolean isInCache(Map<String, OrgObjectDTO> cache, String orgId) {
        return cache.containsKey(orgId);
    }

    protected String getStoreValue(OrgObjectDTO orgObject) {
        if (Objects.isNull(orgObject)) {
            return null;
        }
        return String.format("%s:%s:%s", orgObject.getOrgId(),
            orgObject.getOrgName(),
            orgObject.getOrgType());
    }
}
