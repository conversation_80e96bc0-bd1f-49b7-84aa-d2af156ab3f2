package com.sinitek.sirm.nocode.form.service.impl;

import static com.sinitek.sirm.nocode.form.constant.ZdFormFieldConstant.KEY_FIELD_NAME;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.event.support.SiniCubeEventPublisher;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.frontend.support.PageDataResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.framework.utils.UploadCommonUtils;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskAttachmentDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.common.properties.ZdGlobalProperties;
import com.sinitek.sirm.nocode.common.utils.ZdTempFileUtil;
import com.sinitek.sirm.nocode.form.constant.ExportAndImportTaskExecutorConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataExportConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant;
import com.sinitek.sirm.nocode.form.dao.ZdExportTaskDAO;
import com.sinitek.sirm.nocode.form.dto.ComponentInfoDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportDataErrorMsgDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportDataTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportResourceTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportResourcesFileInfoDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.entity.ZdExportTask;
import com.sinitek.sirm.nocode.form.enumerate.ExportOrImportTaskStatusEnum;
import com.sinitek.sirm.nocode.form.event.ExportResourceTaskCreateEvent;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchResultPO;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.form.support.convert.UploadObjConvert;
import com.sinitek.sirm.nocode.form.support.ctx.ZdFormDataExportContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.ZdComponentValueParserContainer;
import com.sinitek.sirm.nocode.form.util.ZdExportTaskConvertUtil;
import com.sinitek.sirm.nocode.form.util.ZdExportUtil;
import com.sinitek.sirm.nocode.form.util.ZdFormSubmitFieldUtil;
import com.sinitek.sirm.nocode.form.util.ZdImportAndExportUtil;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import com.sinitek.sirm.setting.service.ISettingExtService;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 导出任务Service实现
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class ZdExportTaskServiceImpl implements IZdExportTaskService {

    private static final long DEFAULT_MAX_RESOURCE_FILE_CONTENT_LENGTH = 104857600L;

    private static final Integer DEFAULT_PAGE_SIZE = 10000;

    private static final String EXPORT_EXCEL_DEFAULT_SHEET_NAME = "导出数据";

    /**
     * 导出任务不存在的日志消息
     */
    private static final String LOG_MESSAGE_EXPORT_TASK_NOT_FOUND = "导出任务不存在，taskId:{}";

    /**
     * 导出数据起始行 - 需要合并表头时的起始行
     */
    private static final int EXPORT_DATA_START_ROW_WITH_MERGED_HEAD = 2;

    /**
     * 导出数据起始行 - 普通情况下的起始行
     */
    private static final int EXPORT_DATA_START_ROW_NORMAL = 1;

    @Autowired
    private ZdExportTaskDAO dao;

    @Autowired
    private IZdPageFormDataService pageFormDataService;

    @Autowired
    private IZdPageFormService pageFormService;

    @Autowired
    private IZdPageService pageSerVice;

    @Autowired
    private IAttachmentExtService attachmentExtService;

    @Autowired
    private UploadCommonUtils uploadCommonUtils;

    @Autowired
    private ISettingExtService settingExtService;

    @Autowired
    private SiniCubeEventPublisher eventPublisher;

    @Autowired
    private ZdComponentValueParserContainer parserContainer;

    @Autowired
    private ZdGlobalProperties globalProperties;

    @Lazy
    @Autowired
    private IZdExportTaskService self;

    /**
     * 分页查询导出任务列表
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @Override
    public IPage<ZdExportTaskSearchResultDTO> search(ZdExportTaskSearchParamDTO param) {
        ZdExportTaskSearchParamPO searchParamPO =
                ZdExportTaskConvertUtil.makeSearchParamDTO2PO(param);
        IPage<ZdExportTaskSearchResultPO> search = this.dao.search(searchParamPO);
        return search.convert(ZdExportTaskConvertUtil::makeSearchResultPO2DTO);
    }

    /**
     * 异步执行导出任务
     *
     * @param param 执行参数
     */
    @Async(ExportAndImportTaskExecutorConstant.DEFAULT_EXPORT_TASK_EXECUTOR_NAME)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void runExportTaskAsync(ZdExportDataTaskExecParamDTO param) {
        Long taskId = param.getTaskId();
        log.info("异步执行导出任务开始，taskId: {}", taskId);
        this.doRunExportTask(param);
        log.info("异步执行导出任务完成，taskId: {}", taskId);
    }

    @Async(ExportAndImportTaskExecutorConstant.DEFAULT_RESOURCE_EXPORT_TASK_EXECUTOR_NAME)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void runExportResourceAsync(ZdExportResourceTaskExecParamDTO param) {
        Long taskId = param.getTaskId();
        List<ZdExportResourcesFileInfoDTO> list = param.getList();
        ZdExportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.error(LOG_MESSAGE_EXPORT_TASK_NOT_FOUND, taskId);
            throw new BussinessException(
                    FormDataImportOrExportMessageCodeConstant.EXPORT_TASK_NOT_EXIST);
        }

        try {
            int allFileChunkSize = list.size();
            List<UploadFileDTO> uploadFileList = new LinkedList<>();
            for (int i = 0; i < list.size(); i++) {
                log.info("正在导出导出任务[{}]第[{}]个压缩包,共[{}]个", taskId, i + 1,
                        allFileChunkSize);
                ZdExportResourcesFileInfoDTO item = list.get(i);
                UploadFileDTO uploadFile = this.doRunExportResourceAsync(item, allFileChunkSize);
                if (Objects.nonNull(uploadFile)) {
                    uploadFileList.add(uploadFile);
                }
            }

            UploadDTO upload = new UploadDTO();
            upload.setUploadFileList(uploadFileList);
            upload.setRemoveFileList(Collections.emptyList());
            upload.setType(FormDataExportConstant.EXPORT_RESOURCE_ATTACHMENT_TYPE);
            upload.setSourceEntity(FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY);
            upload.setSourceId(taskId);

            this.attachmentExtService.saveAttachmentList(upload, upload.getSourceId(),
                    upload.getSourceEntity());

            task.setStatus(ExportOrImportTaskStatusEnum.SUCCESS.getValue());
            task.setFileCount(1 + uploadFileList.size());
            task.setErrorMsg(null);
            log.info("导出任务[{}]资源导出成功,共{}个压缩包", taskId, uploadFileList.size());
            this.dao.updateById(task);
        } catch (Exception e) {
            log.error("导出资源任务执行失败，数据: {}", JsonUtil.toJsonString(param), e);
            this.self.updateFailureTaskStatusWithNewTransaction(taskId,
                    ZdExportDataErrorMsgDTO.builder().msg(this.getMessage(e)).build());
        }

    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateFailureTaskStatusWithNewTransaction(Long taskId,
                                                          ZdExportDataErrorMsgDTO errorMsg) {
        ZdExportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.error(LOG_MESSAGE_EXPORT_TASK_NOT_FOUND, taskId);
            throw new BussinessException(
                    FormDataImportOrExportMessageCodeConstant.EXPORT_TASK_NOT_EXIST);
        }
        task.setStatus(ExportOrImportTaskStatusEnum.FAILED.getValue());
        task.setFileCount(0);
        task.setErrorMsg(JsonUtil.toJsonString(errorMsg));
        this.dao.updateById(task);
    }

    private UploadFileDTO doRunExportResourceAsync(ZdExportResourcesFileInfoDTO param,
                                                   int allFileChunkSize)
            throws IOException {
        String formName = param.getFormName();
        Integer fileIndex = param.getFileIndex();
        Date operationTime = param.getOperationTime();
        List<AttachmentDTO> attachments = param.getAttachments();
        Map<String, Long> attachmentsIdCache = param.getAttachmentsIdCache();
        Map<String, String> attachmentsChildIdCache = param.getAttachmentsChildIdCache();

        if (CollUtil.isNotEmpty(attachments)) {
            // 创建一个压缩包
            String zipFileName = ZdExportUtil.getExportResourceZipFileName(formName,
                    operationTime, fileIndex,
                    allFileChunkSize);
            File zipFile = ZdTempFileUtil.createTempFile("export_resource_", ".zip");

            try (ZipOutputStream zipOutputStream = new ZipOutputStream(
                    Files.newOutputStream(zipFile.toPath()))) {
                for (AttachmentDTO attachment : attachments) {
                    Long objId = attachment.getObjId();
                    Long sourceId = attachment.getSourceId();
                    String sourceEntity = attachment.getSourceEntity();
                    Integer type = attachment.getType();
                    String key = ZdExportUtil.getAttachmentCacheKey(sourceId, sourceEntity, type);
                    String fileName = attachment.getName();
                    String fileTypeName = attachment.getFileType();
                    Long id = attachmentsIdCache.get(key);
                    String childId = attachmentsChildIdCache.get(key);

                    String zipEntryPath = ZdExportUtil.getFileHyperlink(id, childId, type, fileName,
                            fileTypeName);

                    try (InputStream inputStream = this.attachmentExtService.getAttachmentAsInputStream(
                            objId)) {
                        ZipEntry zipEntry = new ZipEntry(zipEntryPath);
                        zipOutputStream.putNextEntry(zipEntry);
                        IOUtil.copy(inputStream, zipOutputStream);
                    }
                }
            }

            // 上传压缩包
            UploadFileDTO uploadFile = this.uploadCommonUtils.uploadTempFile(zipFile);
            uploadFile.setName(zipFileName);
            uploadFile.setType(FormDataExportConstant.EXPORT_RESOURCE_ATTACHMENT_TYPE);
            return uploadFile;
        }
        return null;
    }

    private void doRunExportTask(ZdExportDataTaskExecParamDTO param) {
        Long taskId = param.getTaskId();

        ZdExportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.error(LOG_MESSAGE_EXPORT_TASK_NOT_FOUND, taskId);
            throw new BussinessException(
                    FormDataImportOrExportMessageCodeConstant.EXPORT_TASK_NOT_EXIST);
        }

        String exportFileName = task.getFileName();
        Integer status = task.getStatus();
        String operatorId = task.getOperatorId();
        Date operateTime = task.getOperateTime();
        String exportParams = task.getExportParams();
        String formCode = task.getFormCode();

        log.info(
                "导出任务 {},status: {} 开始执行，taskId:{}，operatorId:{}，operateTime:{}，formCode:{}，exportParams:{}",
                exportFileName, status, taskId, operatorId, operateTime, formCode, exportParams);

        try {
            this.doRunExportTask(taskId, task, param);
        } catch (Exception e) {
            log.error(
                    "导出任务 {} 执行失败，taskId:{}，operatorId:{}，operateTime:{}，formCode:{}，exportParams:{}",
                    exportFileName, taskId, operatorId, operateTime, formCode, exportParams, e);
            this.self.updateFailureTaskStatusWithNewTransaction(taskId,
                    ZdExportDataErrorMsgDTO.builder().msg(this.getMessage(e)).build());
        }
    }

    private String getMessage(Exception e) {
        String message = e.getMessage();
        if (StringUtils.isNotBlank(message)) {
            return e.getMessage();
        }
        String name = e.getClass().getName();
        return String.format("执行出现异常: %s", name);
    }

    private void doRunExportTask(Long taskId, ZdExportTask task, ZdExportDataTaskExecParamDTO param) {
        String operatorId = task.getOperatorId();
        Date operateTime = task.getOperateTime();
        String exportParams = task.getExportParams();
        String formCode = task.getFormCode();
        String fileName = task.getFileName();

        String formName = this.pageSerVice.getNameByCode(formCode);

        String accessToken = param.getAccessToken();
        String requestHost = param.getRequestHost();

        ZdFormDataExportParamDTO exportParam =
                JsonUtil.toJavaObject(exportParams, ZdFormDataExportParamDTO.class);
        Boolean exportAttachment = exportParam.getExportAttachment();
        Boolean exportId = exportParam.getExportId();

        ZdFormDataExportContext ctx = new ZdFormDataExportContext();
        ctx.setFormCode(formCode);
        ctx.setAccessToken(accessToken);
        ctx.setRequestHost(requestHost);
        ctx.setExportAttachment(exportAttachment);
        ctx.setExportId(exportId);
        ctx.setAttachmentExtService(this.attachmentExtService);

        // 解析表单schema
        List<ComponentInfoDTO> componentList =
                this.pageFormService.findThreadLatestPublishedComponentInfoList(formCode);

        if (CollUtil.isEmpty(componentList)) {
            log.error("表单[{}]获取到的表单配置为空", formCode);
            throw new BussinessException(
                    FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        List<Long> idList = exportParam.getIdList();

        List<ZdPageFormDataDTO> dataList;
        if (CollUtil.isNotEmpty(idList)) {
            // 查询指定数据
            dataList = this.pageFormDataService.list(formCode, idList);
        } else {
            // 查询所有数据
            ZdFormDataSearchParamDTO queryParam = exportParam.getQueryParam();
            queryParam.setPageIndex(1);
            queryParam.setPageSize(DEFAULT_PAGE_SIZE);
            queryParam.setCurrentOrgId(operatorId);
            TableResult<ZdPageFormDataDTO> searchResult =
                this.pageFormDataService.searchEffectiveData(queryParam);
            PageDataResult<ZdPageFormDataDTO> pageData = searchResult.getData();
            Integer totalsize = pageData.getTotalsize();
            if (totalsize > DEFAULT_PAGE_SIZE) {
                log.warn("导出数据量[{}]大于10000，导出数据不完整", totalsize);
            }
            dataList = pageData.getDatalist();
        }

        log.info("导出总数据量:{}", dataList.size());

        // 设置动态头
        // 子表单中组件: '子表单label','组件label'
        List<List<String>> headList = new ArrayList<>();
        // 合并单元格策略
        List<SheetWriteHandler> sheetWriteHandlerList = new ArrayList<>();

        boolean isDebug = Objects.equals(Boolean.TRUE, this.globalProperties.getExportDebugFlag());

        if (isDebug) {
            log.info("导出任务id: {}", taskId);
            log.info("导出 headList: {}", JsonUtil.toJsonString(headList));
            log.info("导出 componentList: {}", JsonUtil.toJsonString(componentList));
            log.info("导出 exportParam: {}", JsonUtil.toJsonString(exportParam));
            log.info("导出 dataList: {}", JsonUtil.toJsonString(dataList));
            log.info("导出 ctx: {}", JsonUtil.toJsonString(ctx));
            log.info("导出 sheetWriteHandlerList: {}",
                JsonUtil.toJsonString(sheetWriteHandlerList));
        }

        List<List<Object>> excelDataList = this.exeExport(headList, componentList, exportParam,
            dataList, ctx, sheetWriteHandlerList);

        if (isDebug) {
            log.info("导出任务id: {}", taskId);
            log.info("导出数据: {}", JsonUtil.toJsonString(excelDataList));
        }

        File tempFile = ZdTempFileUtil.createExcelTempFile("export_");

        ExcelWriterBuilder write = EasyExcelFactory.write(tempFile);

        write.head(headList);

        ExcelWriterSheetBuilder sheetBuilder = write.sheet(EXPORT_EXCEL_DEFAULT_SHEET_NAME);

        sheetBuilder.registerConverter(
                new UploadObjConvert(this.settingExtService, this.attachmentExtService, ctx));

        if (CollUtil.isNotEmpty(sheetWriteHandlerList)) {
            sheetWriteHandlerList.forEach(sheetBuilder::registerWriteHandler);
        }

        sheetBuilder.doWrite(excelDataList);

        UploadFileDTO uploadTempFile = this.uploadCommonUtils.uploadTempFile(tempFile);
        uploadTempFile.setName(fileName);
        uploadTempFile.setType(FormDataExportConstant.EXPORT_ATTACHMENT_TYPE);

        UploadDTO upload = new UploadDTO();
        upload.setUploadFileList(Collections.singletonList(uploadTempFile));
        upload.setRemoveFileList(Collections.emptyList());
        upload.setType(FormDataExportConstant.EXPORT_ATTACHMENT_TYPE);
        upload.setSourceEntity(FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY);
        upload.setSourceId(taskId);

        List<ZdExportResourcesFileInfoDTO> allZipFileInfo = new LinkedList<>();
        boolean isExportAttachment = ctx.isExportAttachment();
        if (isExportAttachment) {
            Map<String, Long> attachmentsIdCache = ctx.getAttachmentsIdCache();
            Map<String, String> attachmentsChildIdCache = ctx.getAttachmentsChildIdCache();
            List<AttachmentDTO> allAttachments = ctx.getAllAttachments();
            log.info("共有 {} 条附件导出", allAttachments);

            ZdExportResourcesFileInfoDTO currentZipFileInfo = this.buildResourceFileInfo(
                    allZipFileInfo, taskId, formName, operateTime, attachmentsIdCache,
                    attachmentsChildIdCache);
            long currentZipInfoTotalSize = 0L;

            if (CollUtil.isNotEmpty(allAttachments)) {
                for (int i = 0; i < allAttachments.size(); i++) {
                    AttachmentDTO attachment = allAttachments.get(i);
                    Long contentSize = attachment.getContentSize();

                    if ((currentZipInfoTotalSize + contentSize)
                            < DEFAULT_MAX_RESOURCE_FILE_CONTENT_LENGTH) {
                        currentZipInfoTotalSize += currentZipInfoTotalSize;
                    } else {
                        // 新建压缩包
                        currentZipFileInfo = this.buildResourceFileInfo(
                                allZipFileInfo, taskId, formName, operateTime, attachmentsIdCache,
                                attachmentsChildIdCache);
                        currentZipInfoTotalSize = contentSize;
                    }

                    currentZipFileInfo.addAttachment(attachment);
                }
            }
        }

        // 删除旧的附件
        this.attachmentExtService
                .removeAttachment(FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY, taskId);

        this.attachmentExtService.saveAttachmentList(upload, taskId,
                FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY);

        if (isExportAttachment) {
            // 导出附件,由附件任务去处理任务状态
            log.info("导出任务[{}]数据导出成功,开始导出附件,共{}个压缩包", taskId,
                    allZipFileInfo.size());

            if (CollUtil.isNotEmpty(allZipFileInfo)) {
                this.eventPublisher.publishEvent(new ExportResourceTaskCreateEvent(
                        new ZdExportResourceTaskExecParamDTO(taskId, allZipFileInfo)));
            }
        } else {
            task.setStatus(ExportOrImportTaskStatusEnum.SUCCESS.getValue());
            task.setFileCount(1);
            task.setErrorMsg(null);
            this.dao.updateById(task);
            log.info("导出任务[{}]数据导出成功", taskId);
        }
    }

    private List<List<Object>> exeExport(List<List<String>> headList,
        List<ComponentInfoDTO> componentList,
        ZdFormDataExportParamDTO exportParam, List<ZdPageFormDataDTO> dataList,
        ZdFormDataExportContext ctx, List<SheetWriteHandler> sheetWriteHandlerList) {
        Map<String, ComponentInfoDTO> componentRefAndSelfMap = new HashMap<>(componentList.size());
        componentList.forEach(component ->
            componentRefAndSelfMap.put(component.getRef(), component)
        );

        // 子表单中组件: '子表单Ref','组件Ref'
        List<List<String>> valueComponentRefList = new ArrayList<>();

        List<ZdFormFieldDTO> exportFields = exportParam.getExportFields();
        boolean isHeadNeedMerge = false;
        for (ZdFormFieldDTO exportField : exportFields) {
            boolean isCurrentColumnNeedMerge =
                this.parseHead(exportField, null, headList, valueComponentRefList);
            if (isCurrentColumnNeedMerge) {
                isHeadNeedMerge = true;
            }
        }

        // 获取动态数据
        List<List<Object>> excelDataList = new ArrayList<>();

        // 当前行数,从0开始计数,如果需要合并头,则从2开始
        int currentRow =
            isHeadNeedMerge ? EXPORT_DATA_START_ROW_WITH_MERGED_HEAD : EXPORT_DATA_START_ROW_NORMAL;
        for (int i = 0; i < dataList.size(); i++) {
            log.info("开始导出第 {} 条数据", i);

            ZdPageFormDataDTO data = dataList.get(i);
            Long id = data.getId();
            String submitField = data.getSubmitField();
            List<Object> submitFieldList = JsonUtil.toList(
                submitField);

            Map<String, Object> pageData;
            if (CollUtil.isEmpty(submitFieldList)) {
                pageData = new HashMap<>();
            } else {
                pageData = new HashMap<>(submitFieldList.size());
            }

            // 每个子表单中数据最大行数 key:实例id/子表单id
            Map<String, Integer> keyAndMaxRowMap = new HashMap<>();

            Integer maxRows = ZdFormSubmitFieldUtil.getPageData(
                submitFieldList, id,
                null,
                ctx, pageData, data, keyAndMaxRowMap, this.parserContainer);

            // 构建展开后的数据行
            List<List<Object>> expandedRows = new ArrayList<>(maxRows);

            this.buildExpandedRows(exportFields, pageData, keyAndMaxRowMap, maxRows,
                expandedRows,
                currentRow, sheetWriteHandlerList);

            // 添加展开后的数据行到Excel数据列表
            excelDataList.addAll(expandedRows);

            currentRow += expandedRows.size();
        }

        return excelDataList;
    }

    private ZdExportResourcesFileInfoDTO buildResourceFileInfo(
            List<ZdExportResourcesFileInfoDTO> allZipFileInfo, Long taskId, String formName,
            Date operateTime, Map<String, Long> attachmentsIdCache,
            Map<String, String> attachmentsChildIdCache) {
        ZdExportResourcesFileInfoDTO currentZipFileInfo = new ZdExportResourcesFileInfoDTO();
        allZipFileInfo.add(currentZipFileInfo);

        currentZipFileInfo.setFileIndex(allZipFileInfo.size());
        currentZipFileInfo.setSourceId(taskId);
        currentZipFileInfo.setSourceEntity(
                FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY);
        currentZipFileInfo.setType(
                FormDataExportConstant.EXPORT_RESOURCE_ATTACHMENT_TYPE);
        currentZipFileInfo.setFormName(formName);
        currentZipFileInfo.setOperationTime(operateTime);
        currentZipFileInfo.setAttachmentsIdCache(attachmentsIdCache);
        currentZipFileInfo.setAttachmentsChildIdCache(attachmentsChildIdCache);
        return currentZipFileInfo;
    }

    private boolean parseHead(ZdFormFieldDTO exportField, List<String> parentList,
                              List<List<String>> headList,
                              List<List<String>> valueComponentRefList) {
        String currentLabel = exportField.getLabel();
        String currentRef = exportField.getRef();
        List<String> currentHeadList = this.findHeadList(currentLabel, parentList);
        List<String> currentRefList = this.findHeadList(currentRef, parentList);

        List<ZdFormFieldDTO> children = exportField.getChildren();
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(child ->
                    this.parseHead(child, currentHeadList, headList,
                            valueComponentRefList)
            );
            return true;
        } else {
            headList.add(currentHeadList);
            valueComponentRefList.add(currentRefList);
            return false;
        }
    }

    private List<String> findHeadList(String current, List<String> parentList) {
        List<String> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(parentList)) {
            list.addAll(parentList);
        }
        list.add(current);
        return list;
    }

    private void buildExpandedRows(List<ZdFormFieldDTO> exportFields,
                                   Map<String, Object> pageData,
                                   Map<String, Integer> keyAndMaxRowMap, int allRowCount, List<List<Object>> expandedRows,
                                   int currentRow,
                                   List<SheetWriteHandler> sheetWriteHandlerList) {
        // allRowCount 总行数
        // 总列数
        int allColumnCount = ZdImportAndExportUtil.calAllCoumntCount(exportFields);

        for (int row = 0; row < allRowCount; row++) {
            expandedRows.add(new ArrayList<>(allColumnCount));
        }

        int currentExportFieldIndex = 0;
        for (ZdFormFieldDTO exportField : exportFields) {
            String ref = exportField.getRef();
            List<ZdFormFieldDTO> children = exportField.getChildren();

            if (CollUtil.isNotEmpty(children)) {
                // 获取子表单数据列表，每个元素是一条子表单记录
                List<Map<String, Object>> childFormDataList = (List<Map<String, Object>>) MapUtils.getObject(
                        pageData, ref);

                if (CollUtil.isNotEmpty(childFormDataList)) {
                    // 遍历每个字段
                    for (int childColumnIndex = 0; childColumnIndex < children.size();
                         childColumnIndex++) {
                        ZdFormFieldDTO child = children.get(childColumnIndex);
                        // 遍历子表单
                        List<Object> columnList = new LinkedList<>();
                        for (int childDataIndex = 0; childDataIndex < childFormDataList.size();
                             childDataIndex++) {
                            // 子表单数据
                            Map<String, Object> item = childFormDataList.get(childDataIndex);

                            String childFormId = MapUtils.getString(item, KEY_FIELD_NAME);
                            int currentChildFormMaxRow = MapUtils.getIntValue(keyAndMaxRowMap,
                                    childFormId);

                            String childRef = child.getRef();
                            if (CollUtil.isNotEmpty(childFormDataList)) {
                                Object childFormFieldData = MapUtils.getObject(
                                        item, childRef);
                                if (childFormFieldData instanceof List) {
                                    columnList.addAll((List<Object>) childFormFieldData);
                                } else {
                                    columnList.addAll(
                                            ZdImportAndExportUtil.getFilledList(childFormFieldData,
                                                    currentChildFormMaxRow));

                                    if (currentChildFormMaxRow > 1) {
                                        OnceAbsoluteMergeStrategy onceAbsoluteMergeStrategy = new OnceAbsoluteMergeStrategy(
                                                currentRow + childDataIndex,
                                                currentRow + childDataIndex + currentChildFormMaxRow
                                                        - 1,
                                                childColumnIndex + currentExportFieldIndex,
                                                childColumnIndex + currentExportFieldIndex);
                                        sheetWriteHandlerList.add(onceAbsoluteMergeStrategy);
                                    }
                                }
                            }
                        }
                        ZdImportAndExportUtil.fillListWithList(expandedRows, columnList);
                    }
                }

                currentExportFieldIndex += children.size();
            } else {
                // 处理非子表单字段（普通字段、附件字段）
                Object value = MapUtils.getObject(pageData, ref);

                if (value instanceof List) {
                    // 附件字段：List类型，需要按行展开
                    List<Object> listValue = (List<Object>) value;
                    ZdImportAndExportUtil.fillListWithList(expandedRows, listValue);
                } else {
                    // 普通字段：单值，在所有行中重复显示
                    ZdImportAndExportUtil.fillList(expandedRows, value, allRowCount);

                    if (allRowCount > 1) {
                        OnceAbsoluteMergeStrategy onceAbsoluteMergeStrategy = new OnceAbsoluteMergeStrategy(
                                currentRow, currentRow + allRowCount - 1, currentExportFieldIndex,
                                currentExportFieldIndex);
                        sheetWriteHandlerList.add(onceAbsoluteMergeStrategy);
                    }
                }
                currentExportFieldIndex++;
            }
        }
    }

    @Override
    public ZdExportDataErrorMsgDTO getExportTaskErrorDetail(Long taskId) {
        ZdExportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导出任务[{}]不存在", taskId);
            return null;
        }
        return JsonUtil.toJavaObject(task.getErrorMsg(), ZdExportDataErrorMsgDTO.class);
    }

    @Override
    public List<ZdExportTaskAttachmentDTO> findExportExcelAttachment(Long taskId) {
        List<AttachmentDTO> attachmentList = this.attachmentExtService.findAttachmentList(
                FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY,
                taskId);
        if (CollUtil.isNotEmpty(attachmentList)) {
            return attachmentList.stream().map(
                    item -> ZdExportTaskAttachmentDTO.builder().id(item.getId())
                            .fileName(item.getName())
                            .build()).collect(Collectors.toList());
        } else {
            log.warn("导出任务[{}]未找到附件", taskId);
        }
        return Collections.emptyList();
    }

    @Override
    public String getExportExcelAttachmentId(Long taskId) {
        List<AttachmentDTO> attachmentList = this.attachmentExtService.findAttachments(
                FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY,
                FormDataExportConstant.EXPORT_ATTACHMENT_TYPE, Collections.singletonList(taskId));
        if (CollUtil.isNotEmpty(attachmentList)) {
            return attachmentList.get(0).getId();
        } else {
            log.warn("导出任务[{}]未找到附件", taskId);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cleanHistoryExportTask(int days) {

        LambdaQueryWrapper<ZdExportTask> lambdaQuery = Wrappers.lambdaQuery(ZdExportTask.class)
                .eq(ZdExportTask::getStatus, ExportOrImportTaskStatusEnum.SUCCESS.getValue())
                .lt(ZdExportTask::getOperateTime, DateUtil.offsetDay(new Date(), -days))
                .select(ZdExportTask::getId);

        List<Long> taskIdList = this.dao.listObjs(lambdaQuery, result -> (Long) result);

        if (CollUtil.isNotEmpty(taskIdList)) {
            List<AttachmentDTO> attachmentList = this.attachmentExtService.findAttachments(
                    FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY,
                    FormDataExportConstant.EXPORT_ATTACHMENT_TYPE, taskIdList);
            List<AttachmentDTO> resourceAttachmentList = this.attachmentExtService.findAttachments(
                    FormDataExportConstant.EXPORT_ATTACHMENT_SOURCE_ENTITY,
                    FormDataExportConstant.EXPORT_RESOURCE_ATTACHMENT_TYPE, taskIdList);

            List<AttachmentDTO> allAttachmentList = new LinkedList<>();
            if (CollUtil.isNotEmpty(attachmentList)) {
                allAttachmentList.addAll(attachmentList);
            }
            if (CollUtil.isNotEmpty(resourceAttachmentList)) {
                allAttachmentList.addAll(resourceAttachmentList);
            }

            if (CollUtil.isNotEmpty(allAttachmentList)) {
                List<Long> attachmentIdList = allAttachmentList.stream()
                        .map(AttachmentDTO::getObjId).collect(Collectors.toList());
                this.attachmentExtService.batchRemoveAttachment(attachmentIdList);
            }

            this.dao.removeByIds(taskIdList);

            log.info("删除 {} 天前导出任务成功,共删除 {} 条任务", days, taskIdList.size());
        } else {
            log.info("删除 {} 天前导出任务成功,未找到任务", days);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkExportTaskStatus(int minutes) {
        LambdaQueryWrapper<ZdExportTask> lambdaQuery = Wrappers.lambdaQuery(ZdExportTask.class)
                .in(ZdExportTask::getStatus, ExportOrImportTaskStatusEnum.PROCESSING.getValue(),
                        ExportOrImportTaskStatusEnum.PENDING.getValue())
                .lt(ZdExportTask::getOperateTime, DateUtil.offsetMinute(new Date(), -minutes));

        List<ZdExportTask> taskList = this.dao.list(lambdaQuery);

        if (CollUtil.isNotEmpty(taskList)) {
            taskList.forEach(item -> {
                item.setStatus(ExportOrImportTaskStatusEnum.FAILED.getValue());
                item.setFileCount(0);
                item.setErrorMsg(JsonUtil.toJsonString(new ZdExportDataErrorMsgDTO("任务处理超时")));
            });
            this.dao.updateBatchById(taskList);
            log.info("检查导出任务状态成功,共检查 {} 条任务", taskList.size());
        } else {
            log.info("检查导出任务状态成功,未找到任务");
        }
    }
}
