package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.SplitResult;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.AbstractOrgComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import com.sinitek.sirm.nocode.form.util.ZdStringSplitUtil;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:22
 */
@Slf4j
public class ZdDepartmentComponentValueParser extends AbstractOrgComponentValueParser implements
        IZdComponentValueParser {

    public ZdDepartmentComponentValueParser(IOrgService orgService) {
        super(orgService);
    }

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_DEPARTMENT.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
                                            Map<String, Object> convertedData,
                                            Map<String, Object> sourceMap, ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        boolean multipleFlag = component.getMultipleFlag();

        String name = MapUtils.getString(sourceMap, ref);
        if (StringUtils.isBlank(name)) {
            return;
        }

        ZdImportContext zdImportContext = ctx.getCtx();
        if (multipleFlag) {
            String orgIds = Arrays.stream(name.split(","))
                    .map(singleName -> {
                        OrgObjectDTO orgObject = this.getOrgObject(ctx, singleName);
                        zdImportContext.putOrgIdAndOrgObjectMap(orgObject.getOrgId(), orgObject);
                        return orgObject;
                    })
                    .map(OrgObjectDTO::getOrgId)
                    .collect(Collectors.joining(","));
            convertedData.put(ref, orgIds);
        } else {
            OrgObjectDTO orgObject = this.getOrgObject(ctx, name);
            zdImportContext.putOrgIdAndOrgObjectMap(orgObject.getOrgId(), orgObject);
            convertedData.put(ref, orgObject.getOrgId());
        }
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, Object> toSubmitFileBlock(ZdPageComponentDTO pageComponent,
                                                 Map<String, Object> valueMap, ZdParseContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();
        boolean multipleFlag = pageComponent.getMultipleFlag();
        String rowOrgId = MapUtils.getString(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (StringUtils.isBlank(rowOrgId)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        List<String> orgIds = this.initOrgCache(ctx, multipleFlag, rowOrgId);
        Map<String, OrgObjectDTO> orgIdAndOrgObjectMap = ctx.getOrgIdAndOrgObjectMap();
        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        if (multipleFlag) {
            List<String> orgIdNames = new LinkedList<>();
            List<String> orgDisplayValues = new LinkedList<>();

            orgIds.forEach(singleOrgId -> {
                OrgObjectDTO orgObject = orgIdAndOrgObjectMap.get(singleOrgId);
                orgIdNames.add(this.getStoreValue(orgObject));
                orgDisplayValues.add(orgObject.getOrgName());
            });

            submitField.put(FormSubmitFieldConstant.VALUE,
                    String.join(",", orgIdNames));
            submitField.put(FormSubmitFieldConstant.DISPLAY_VALUE,
                    String.join(",", orgDisplayValues));
        } else {
            OrgObjectDTO orgObject = orgIdAndOrgObjectMap.get(rowOrgId);
            submitField.put(FormSubmitFieldConstant.VALUE,
                    this.getStoreValue(orgObject));
            submitField.put(FormSubmitFieldConstant.DISPLAY_VALUE, orgObject.getOrgName());
        }

        return submitField;
    }

    private OrgObjectDTO getOrgObject(
            ZdParseComponentValueContext ctx, String name) {

        ZdImportContext importContext = ctx.getCtx();
        Map<String, List<OrgObjectDTO>> deptNameAndOrgIdMap = importContext.getDeptNameAndOrgIdMap();
        Map<String, List<OrgObjectDTO>> positionNameAndOrgIdMap = importContext.getPositionNameAndOrgIdMap();
        Map<String, List<OrgObjectDTO>> roleNameAndOrgIdMap = importContext.getRoleNameAndOrgIdMap();
        Map<String, List<OrgObjectDTO>> teamNameAndOrgIdMap = importContext.getTeamNameAndOrgIdMap();

        Map<String, List<OrgObjectDTO>> allMap = new HashMap<>(
                deptNameAndOrgIdMap.size() + positionNameAndOrgIdMap.size() + roleNameAndOrgIdMap.size()
                        + teamNameAndOrgIdMap.size());
        allMap.putAll(deptNameAndOrgIdMap);
        allMap.putAll(positionNameAndOrgIdMap);
        allMap.putAll(roleNameAndOrgIdMap);
        allMap.putAll(teamNameAndOrgIdMap);
        return this.getMatchValue(name, allMap);
    }

    private OrgObjectDTO getMatchValue(String originValue,
                                       Map<String, List<OrgObjectDTO>> map) {
        List<OrgObjectDTO> orgObjects;

        SplitResult splitResult = ZdStringSplitUtil.splitOrgName(originValue);
        boolean isNeedSplit = Objects.equals(Boolean.TRUE, splitResult.getNeedSplitFlag());

        // 提取括号内数据
        // 张三,张三(112033)
        if (isNeedSplit) {
            String orgId = splitResult.getOtherName();
            String rowName = splitResult.getOrgName();
            orgObjects = map.get(rowName);
            if (CollUtil.isNotEmpty(orgObjects)) {
                // 根据原始名称匹配到了
                if (orgObjects.size() == 1) {
                    return orgObjects.get(0);
                } else {
                    // 匹配到多条,根据括号内数据进一步区分
                    orgObjects = orgObjects.stream().filter(orgObject ->
                                    StringUtils.equalsIgnoreCase(orgObject.getOrgId(), orgId))
                            .collect(
                                    Collectors.toList());
                    if (CollUtil.isNotEmpty(orgObjects)) {
                        if (orgObjects.size() == 1) {
                            // 根据orgId匹配到了
                            return orgObjects.get(0);
                        } else {
                            log.error(
                                    "{} 中提取的名称:{},orgId:{} 存在多条匹配的组织结构数据 {}",
                                    originValue, rowName,
                                    orgId,
                                    JsonUtil.toJsonString(orgObjects));
                            this.throwParseException(
                                    String.format("【%s】存在多条对应的组织结构数据", originValue));
                        }
                    }
                }
            } else {
                log.error(
                        "{} 中提取的名称:{},orgId:{} 没有匹配的组织结构数据 {}",
                        originValue, rowName,
                        orgId,
                        JsonUtil.toJsonString(orgObjects));
                this.throwParseException(
                        String.format("【%s】没有对应的组织结构数据", originValue));
            }
        } else {
            orgObjects = map.get(originValue);
        }

        if (CollUtil.isEmpty(orgObjects)) {
            log.error("{} 没有匹配的组织结构数据 {}", originValue,
                    JsonUtil.toJsonString(map));
            this.throwParseException(String.format("【%s】没有对应的组织结构数据", originValue));
        }
        if (orgObjects.size() > 1) {
            log.error("{} 存在多条匹配的组织结构数据 {}", originValue,
                    JsonUtil.toJsonString(orgObjects));
            this.throwParseException(String.format("【%s】存在多条对应的组织结构数据", originValue));
        }
        return orgObjects.get(0);
    }
}

