package com.sinitek.sirm.nocode.form.support.handler.pre;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.nocode.common.enumerate.SaveOrUpdateEnum;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.handler.base.PreDataSaveOrUpdateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 附件上传处理
 *
 * <AUTHOR>
 * @version 2025.0702
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class UploadFileHandler implements PreDataSaveOrUpdateHandler {

    @Resource
    private IAttachmentExtService attachmentExtService;

    @Resource
    private Validator validator;


    /**
     * 处理表单数据中的文件上传操作
     *
     * @param formData 表单数据对象
     * @param type     操作类型（保存或更新）
     * @implNote 实际调用uploadFile方法进行文件处理
     */
    @Override
    public void handler(ZdPageFormData formData, SaveOrUpdateEnum type) {
        Map<String, Object> formDataMap = formData.getFormDataMap();
        // 存在上传文件时，才处理上传文件
        AtomicBoolean hasUploadFile = new AtomicBoolean(false);
        uploadFile(formDataMap, formData, hasUploadFile);
        if (hasUploadFile.get()) {
            formData.setFormData(JsonUtil.toJsonString(formDataMap));
        }

    }

    /**
     * 递归处理表单中的文件上传字段
     *
     * @param formDataMap 表单数据映射
     * @param formData    主表单数据对象
     * @implNote 支持处理嵌套子表单中的文件上传字段
     */

    private void uploadFile(Map<String, Object> formDataMap, ZdPageFormData formData, AtomicBoolean hasUploadFile) {
        formDataMap.forEach((key, value) -> {
            PageDataComponentTypeEnum pageDataComponentTypeEnum = PageDataComponentTypeEnum.formKey(key);
            handleComponent(pageDataComponentTypeEnum, key, value, formDataMap, formData, hasUploadFile);
        });
    }

    /**
     * 根据组件类型处理不同的表单字段
     *
     * @param type          组件类型枚举，用于判断当前字段的处理方式
     * @param key           表单字段的键名
     * @param value         表单字段的值
     * @param formDataMap   表单数据映射，用于更新处理后的数据
     * @param formData      主表单数据对象，用于获取表单相关上下文信息
     * @param hasUploadFile 用于标记是否包含上传文件的原子布尔值
     * @implNote 如果是上传组件（ZD_UPLOAD），则调用handleUploadComponent方法处理；
     * 如果是子表单组件（ZD_CHILD_FORM）且值为List类型，则调用handleChildFormComponent方法处理
     */
    private void handleComponent(PageDataComponentTypeEnum type, String key, Object value,
                                 Map<String, Object> formDataMap, ZdPageFormData formData, AtomicBoolean hasUploadFile) {
        if (PageDataComponentTypeEnum.ZD_UPLOAD.equals(type)) {
            handleUploadComponent(key, value, formDataMap, formData, hasUploadFile);
        } else if (PageDataComponentTypeEnum.ZD_CHILD_FORM.equals(type) && (value instanceof List)) {
            handleChildFormComponent(value, formData, hasUploadFile);
        }
    }


    @SuppressWarnings("unchecked")
    /**
     * 处理上传组件的文件上传逻辑
     *
     * @param key           表单字段键名
     * @param value         表单字段值，应为Map类型，包含上传文件相关信息
     * @param formDataMap   表单数据映射，用于更新处理后的数据
     * @param formData      主表单数据对象，用于获取表单相关上下文信息
     * @param hasUploadFile 用于标记是否包含上传文件的原子布尔值
     * @implNote 该方法会解析上传组件数据，校验参数，处理附件列表，并更新表单数据映射
     */
    private void handleUploadComponent(String key, Object value, Map<String, Object> formDataMap,
                                       ZdPageFormData formData, AtomicBoolean hasUploadFile) {
        // 检查值是否为Map类型，如果不是则直接返回
        if (!(value instanceof Map)) {
            return;
        }

        // 将值转换为UploadDTO对象
        String jsonString = JsonUtil.toJsonString(value);
        UploadDTO uploadDTO = JsonUtil.toJavaObject(jsonString, UploadDTO.class);

        // 如果sourceEntity为空，则设置为表单编码
        String sourceEntity = uploadDTO.getSourceEntity();
        if (StringUtils.isBlank(sourceEntity)) {
            uploadDTO.setSourceEntity(formData.getFormCode());
        }

        // 校验上传文件参数
        validateUploadDTO(uploadDTO);

        // 获取上传和删除的文件列表
        List<UploadFileDTO> uploadFileList = uploadDTO.getUploadFileList();
        List<UploadFileDTO> removeFileList = uploadDTO.getRemoveFileList();

        // 如果存在上传或删除的文件，则处理附件列表
        if (CollectionUtils.isNotEmpty(uploadFileList) || CollectionUtils.isNotEmpty(removeFileList)) {
            processAttachmentList(uploadDTO);
        }

        // 提取value中的Map数据
        Map<String, Object> valueMap = (Map<String, Object>) value;

        // 创建新的Map，只保留sourceId和type字段
        Map<String, Object> map = new HashMap<>();
        map.put("sourceId", valueMap.get("sourceId"));
        map.put("type", valueMap.get("type"));

        // 删除其他属性，只保留sourceId和type
        formDataMap.put(key, map);

        // 标记存在上传文件
        hasUploadFile.set(true);
    }


    @SuppressWarnings("unchecked")
    /**
     * 处理子表单中的文件上传组件
     *
     * @param value         子表单数据列表，每个元素为一个Map，代表一行子表单数据
     * @param formData      主表单数据对象，用于获取表单相关上下文信息
     * @param hasUploadFile 用于标记是否包含上传文件的原子布尔值
     * @implNote 遍历子表单数据列表，递归调用uploadFile方法处理每个子表单中的文件上传字段
     */
    private void handleChildFormComponent(Object value, ZdPageFormData formData, AtomicBoolean hasUploadFile) {
        List<Map<String, Object>> childFormDataList = (List<Map<String, Object>>) value;
        if (CollectionUtils.isNotEmpty(childFormDataList)) {
            childFormDataList.forEach(childFormData -> uploadFile(childFormData, formData, hasUploadFile));
        }
    }

    /**
     * 校验上传文件参数
     *
     * @param uploadDTO 上传文件参数对象
     * @throws ConstraintViolationException 当校验不通过时抛出此异常
     */
    private void validateUploadDTO(UploadDTO uploadDTO) {
        Set<ConstraintViolation<UploadDTO>> validate = validator.validate(uploadDTO);
        if (CollectionUtils.isNotEmpty(validate)) {
            throw new ConstraintViolationException(validate);
        }
    }

    /**
     * 处理附件列表，调用服务保存附件并记录上传成功的文件ID
     *
     * @param uploadDTO 上传文件参数对象，包含待处理的文件列表和相关信息
     */
    private void processAttachmentList(UploadDTO uploadDTO) {
        List<AttachmentDTO> attachmentDTOS = attachmentExtService.saveAttachmentList(uploadDTO, uploadDTO.getSourceId(), uploadDTO.getSourceEntity());
        if (CollectionUtils.isNotEmpty(attachmentDTOS)) {
            String attachmentIds = attachmentDTOS.stream().map(AttachmentDTO::getId).collect(Collectors.joining(","));
            log.info("上传文件成功，文件id为：{}", attachmentIds);
        }
    }
}
