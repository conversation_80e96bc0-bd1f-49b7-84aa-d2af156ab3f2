package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.common.component.dto.XnSelectDTO;
import com.sinitek.sirm.nocode.app.service.IZdAppRightService;
import com.sinitek.sirm.nocode.common.tree.TreeDataMaker;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageRuntimeDTO;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageRuntimeService;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0820
 * @since 1.0.0-SNAPSHOT
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZdPageRuntimeServiceImpl implements IZdPageRuntimeService {
    private final IZdPageService zdPageService;
    private final IZdPageAuthService pageAuthService;
    private final IZdAppRightService appRightService;

    @Override
    public List<ZdPageRuntimeDTO> publishedListTree(String appCode, String orgId) {
        List<ZdPageRuntimeDTO> list = zdPageService.publishedList(appCode);
        String errorCode = appRightService.checkAppRight(appCode);
        if (Objects.isNull(errorCode)) {
            // 管理员
            list.forEach(a -> {
                a.setHasDataRight(true);
                a.setHasSubmitRight(true);
            });
        } else {
            List<String> pageCodeList =
                    list.stream().map(a -> {
                        PageTypeEnum pageType = a.getPageType();
                        if (!Objects.equals(pageType, PageTypeEnum.GROUP_PAGE)) {
                            return a.getCode();
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList());


            List<ZdPageAuthDTO> pageAuthDTOS = pageAuthService.findByPageCodeListSimple(pageCodeList);
            Map<String, List<ZdPageAuthDTO>> map = pageAuthDTOS.stream().collect(Collectors.groupingBy(a -> a.getFormCode() + "_" + a.getAuthType().getValue()));
            List<ZdPageRuntimeDTO> removedList = new ArrayList<>();
            list.forEach(page -> {
                if (Objects.equals(page.getPageType(), PageTypeEnum.GROUP_PAGE)) {
                    return;
                }
                // 提交权限
                String submitKey = page.getCode() + "_" + PageAuthTypeEnum.SUBMIT_AUTH.getValue();
                List<ZdPageAuthDTO> submitAuthList = map.get(submitKey);
                boolean hasSubmitAuth = pageAuthService.hasRight(submitAuthList, orgId);
                page.setHasSubmitRight(hasSubmitAuth);
                String dataManagerKey = page.getCode() + "_" + PageAuthTypeEnum.DATA_AUTH.getValue();
                List<ZdPageAuthDTO> dataAuthList = map.get(dataManagerKey);
                boolean hasDataAuth = pageAuthService.hasRight(dataAuthList, orgId);
                page.setHasDataRight(hasDataAuth);
                if (!hasDataAuth && !hasSubmitAuth) {
                    removedList.add(page);
                }
            });
            if (CollectionUtils.isNotEmpty(removedList)) {
                list.removeAll(removedList);
            }
        }
        return TreeDataMaker.tree(list, a -> ZdPageDTO.filterEmptyGroup(ConvertUtil.convert(a)));
    }

    @Override
    public String getFirstDefaultFormCode(String appCode, String orgId) {
        String errorCode = appRightService.checkAppRight(appCode);
        if (Objects.isNull(errorCode)) {
            // 管理员
            return zdPageService.getDefaultFormCode(appCode);
        }
        // 不是管理员的话
        List<String> pageCodeList = zdPageService.findAllForm(appCode).stream().map(XnSelectDTO::getValue).collect(Collectors.toList());
        for (String formCode : pageCodeList) {
            ZdPageAuthDTO zdPageAuthDTO = pageAuthService.rightQuery(formCode, PageAuthTypeEnum.SUBMIT_AUTH, orgId);
            if (Objects.nonNull(zdPageAuthDTO)) {
                return formCode;
            }
        }
        return null;
    }
}
