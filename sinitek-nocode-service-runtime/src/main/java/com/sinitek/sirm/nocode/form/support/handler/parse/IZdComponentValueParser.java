package com.sinitek.sirm.nocode.form.support.handler.parse;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseExportValueContext;
import com.sinitek.sirm.nocode.form.support.handler.ZdExportDataParseResult;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:09
 */
public interface IZdComponentValueParser {

    String getMatchedComponentName();

    default boolean isDefaultParser() {
        return false;
    }

    /**
     * 解析组件数据生成formData的key,value对
     *
     * 允许报错
     */
    void parseComponentValueOnImport(ZdPageComponentDTO component,
        Map<String, Object> convertedData, Map<String, Object> sourceMap,
        ZdParseComponentValueContext ctx) throws Exception;

    Map<String, Object> toSubmitFileBlock(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdParseContext ctx);

    /**
     * 返回null代表没有submitField
     *
     * 这里不会出现异常,因为前面 parseComponentValueOnImport 会对数据进行校验
     */
    default Map<String, Object> toSubmitFileBlockOnImport(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdImportContext ctx) {
        return this.toSubmitFileBlock(pageComponent, valueMap, ctx);
    }

    default ZdExportDataParseResult getExportData(Map<String, Object> submitFieldMap,
        ZdParseExportValueContext ctx) {
        Object value = MapUtils.getObject(submitFieldMap,
            FormSubmitFieldConstant.VALUE);
        Object displayValue = MapUtils.getObject(submitFieldMap,
            FormSubmitFieldConstant.DISPLAY_VALUE);

        Object exportValue;
        if (Objects.nonNull(displayValue)) {
            exportValue = displayValue;
        } else {
            exportValue = value;
        }

        return ZdExportDataParseResult.builder().value(exportValue).maxRow(1).build();
    }


    default void throwParseException(String msg) {
        throw new BussinessException(true, msg);
    }
}
