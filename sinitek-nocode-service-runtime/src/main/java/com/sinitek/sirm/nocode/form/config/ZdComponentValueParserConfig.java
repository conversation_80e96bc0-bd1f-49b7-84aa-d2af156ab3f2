package com.sinitek.sirm.nocode.form.config;

import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.ZdComponentValueParserContainer;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.DefaultZdComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZDAssociationComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdCheckboxComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdChildFormComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdDateComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdDateRangeComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdDepartmentComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdEmployeeComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdRadioComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdSelectMultiComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdSelectSingleComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.impl.ZdUploadComponentValueParser;
import com.sinitek.sirm.org.service.IOrgService;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:40
 */
@Slf4j
@Configuration
public class ZdComponentValueParserConfig {

    @Bean
    @ConditionalOnMissingBean
    public DefaultZdComponentValueParser defaultZdComponentValueParser() {
        return new DefaultZdComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdCheckboxComponentValueParser zdCheckboxComponentValueParser() {
        return new ZdCheckboxComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdDateComponentValueParser zdDateComponentValueParser() {
        return new ZdDateComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdDateRangeComponentValueParser zdDateRangeComponentValueParser() {
        return new ZdDateRangeComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdRadioComponentValueParser zdRadioComponentValueParser() {
        return new ZdRadioComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdSelectMultiComponentValueParser zdSelectMultiComponentValueParser() {
        return new ZdSelectMultiComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdSelectSingleComponentValueParser zdSelectSingleComponentValueParser() {
        return new ZdSelectSingleComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdUploadComponentValueParser zdUploadComponentValueParser() {
        return new ZdUploadComponentValueParser();
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdChildFormComponentValueParser zdChildFormComponentValueParser(
        @Lazy ZdComponentValueParserContainer parserContainer) {
        return new ZdChildFormComponentValueParser(parserContainer);
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdEmployeeComponentValueParser zdEmployeeComponentValueParser(IOrgService orgService) {
        return new ZdEmployeeComponentValueParser(orgService);
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdDepartmentComponentValueParser zdDepartmentComponentValueParser(
        IOrgService orgService) {
        return new ZdDepartmentComponentValueParser(orgService);
    }

    @Bean
    @ConditionalOnMissingBean
    public ZDAssociationComponentValueParser zdAssociationComponentValueParser(
        IZdPageFormDataService zdPageFormDataService) {
        return new ZDAssociationComponentValueParser(zdPageFormDataService);
    }

    @Bean
    @ConditionalOnMissingBean
    public ZdComponentValueParserContainer zdComponentValueParserContainer(
        ObjectProvider<IZdComponentValueParser> parserObjectProvider) {
        List<IZdComponentValueParser> parsers = parserObjectProvider.orderedStream()
            .collect(Collectors.toList());
        return new ZdComponentValueParserContainer(parsers);
    }

}
