package com.sinitek.sirm.nocode.form.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;

/**
 * 检查长时间处于"处理中"状态的任务，超过1小时自动标记为失败
 *
 * <AUTHOR>
 * @date 2025-07-16 10:08
 */
@Component
public class ExportTaskStatusCheckJob {

  /**
   * 导出任务超时时间（分钟）
   */
  private static final int TASK_TIMEOUT_MINUTES = 60;

  @Autowired
  private IZdExportTaskService exportTaskService;

  @Scheduled(cron = "0 0/5 * * * ?")
  public void checkExportTaskStatus() {
    this.exportTaskService.checkExportTaskStatus(TASK_TIMEOUT_MINUTES);
  }

}