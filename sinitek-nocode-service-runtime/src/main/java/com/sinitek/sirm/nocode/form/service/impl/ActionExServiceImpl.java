package com.sinitek.sirm.nocode.form.service.impl;

import com.sinitek.sirm.common.action.dto.ActionExResultDTO;
import com.sinitek.sirm.common.action.dto.BindActionDTO;
import com.sinitek.sirm.common.action.dto.TriggerActionDTO;
import com.sinitek.sirm.common.action.dto.UnBindActionDTO;
import com.sinitek.sirm.common.action.service.IActionExService;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-07-08 14:31
 */
@Slf4j
public class ActionExServiceImpl implements IActionExService {

    @Override
    public <T> void bindAction(BindActionDTO<T> dto) {
        log.warn("方法 bindAction 尚未实现");
    }

    @Override
    public void unBindAction(UnBindActionDTO dto) {
        log.warn("方法 unBindAction 尚未实现");
    }

    @Override
    public void unBindAction(List<Long> ids) {
        log.warn("方法 unBindAction 根据ID列表解绑尚未实现");
    }

    @Override
    public boolean isBindAction(String sourceId, String sourceName) {
        log.warn("方法 isBindAction 尚未实现");
        return false;
    }

    @Override
    public <T> List<ActionExResultDTO> triggerAction(TriggerActionDTO<T> dto) {
        log.warn("方法 triggerAction 尚未实现");
        return Collections.emptyList();
    }
}