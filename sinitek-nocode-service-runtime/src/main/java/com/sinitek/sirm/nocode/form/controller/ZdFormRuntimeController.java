package com.sinitek.sirm.nocode.form.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCreateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaRemoveParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaUpdateParamDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import com.sinitek.sirm.nocode.page.dto.ZdPageRuntimeDTO;
import com.sinitek.sirm.nocode.page.service.IZdPageRuntimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/runtime", tags = "运行时接口管理")
@RequestMapping("/frontend/api/nocode/runtime")
@RequiredArgsConstructor
public class ZdFormRuntimeController {
    private final IZdPageRuntimeService pageRuntimeService;
    private final IZdPageFormInstanceSchemaService schemaService;

    @ApiOperation(value = "运行时页面列表", notes = "运行时，已经发布的页面列表")
    @GetMapping("/page/list")
    public RequestResult<List<ZdPageRuntimeDTO>> list(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(pageRuntimeService.publishedListTree(appCode, CurrentUserFactory.getOrgId()));
    }

    @ApiOperation(value = "运行时，获取当前登陆人指定应用下第一个拥有提交权限的表单编码")
    @GetMapping("/page/get-first-form-code")
    public RequestResult<String> getFirstFormCode(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(pageRuntimeService.getFirstDefaultFormCode(appCode, CurrentUserFactory.getOrgId()));
    }


    /**
     * 保存页面实例Schema
     *
     * @param param 创建参数
     * @return Schema ID
     */
    @ApiOperation(value = "保存页面实例Schema")
    @PostMapping("/save-instance-schema")
    public RequestResult<String> saveInstanceSchema(
            @Valid @RequestBody ZdFormInstanceSchemaCreateParamDTO param) {
        String schemaId = this.schemaService.createSchemaCache(param);
        return RequestResult.success(schemaId);
    }

    /**
     * 更新页面实例Schema
     *
     * @param param 更新参数
     * @return 操作结果
     */
    @ApiOperation(value = "更新页面实例Schema")
    @PostMapping("/update-instance-schema")
    public RequestResult<Void> updateInstanceSchema(
            @Valid @RequestBody
            ZdFormInstanceSchemaUpdateParamDTO param) {
        this.schemaService.updateSchemaCache(param);
        return RequestResult.success();
    }

    /**
     * 卸载页面实例Schema
     *
     * @param request Schema ID
     * @return 操作结果
     */
    @ApiOperation(value = "卸载页面实例Schema")
    @PostMapping("/remove-instance-schema")
    public RequestResult<Void> removeInstanceSchema(
            @Valid @RequestBody
            ZdFormInstanceSchemaRemoveParamDTO request) {
        String schemaId = request.getSchemaId();
        this.schemaService.deleteSchemaCache(schemaId);
        return RequestResult.success();
    }

    /**
     * 获取页面实例Schema
     *
     * @param schemaId Schema ID
     * @return Schema缓存数据
     */
    @ApiOperation(value = "获取页面实例Schema")
    @GetMapping("/get-instance-schema")
    public RequestResult<ZdFormInstanceSchemaCacheDTO> getInstanceSchema(
            @RequestParam @ApiParam(value = "schemaId", required = true) String schemaId) {
        ZdFormInstanceSchemaCacheDTO result = this.schemaService.getSchemaCache(schemaId);
        return new RequestResult<>(result);
    }

    @ApiOperation(value = "获取页面实例Schema Id")
    @GetMapping("/list-schema-id")
    public RequestResult<List<String>> listSchemaIds() {
        return new RequestResult<>(this.schemaService.findCachedSchemaId());
    }
}
