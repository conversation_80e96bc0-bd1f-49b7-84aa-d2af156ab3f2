package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.ctx.ZdFormDataExportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseExportValueContext;
import com.sinitek.sirm.nocode.form.support.handler.ZdExportDataParseResult;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.ZdComponentValueParserContainer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sinitek.sirm.nocode.form.constant.ZdFormFieldConstant.KEY_FIELD_NAME;
import static com.sinitek.sirm.nocode.form.util.ZdFormSubmitFieldUtil.getPageData;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:25
 */
@Slf4j
public class ZdChildFormComponentValueParser implements IZdComponentValueParser {

    protected ZdComponentValueParserContainer parserContainer;

    public ZdChildFormComponentValueParser(ZdComponentValueParserContainer componentValueParser) {
        this.parserContainer = componentValueParser;
    }

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_CHILD_FORM.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
                                            Map<String, Object> convertedData,
                                            Map<String, Object> sourceMap, ZdParseComponentValueContext ctx) {
        // IGNORE
        log.debug("子表单组件无需解析组件值");
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, Object> toSubmitFileBlock(ZdPageComponentDTO pageComponent,
                                                 Map<String, Object> valueMap, ZdParseContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);

        List<ZdPageComponentDTO> children = pageComponent.getChildren();
        List<Map<String, Object>> list = (List<Map<String, Object>>) value;
        List<List<Map<String, Object>>> childrenSubmitFieldList = new LinkedList<>();
        for (Map<String, Object> item : list) {
            List<Map<String, Object>> childrenSubmitFieldInnerValue = new LinkedList<>();
            for (ZdPageComponentDTO child : children) {
                String childComponentName = child.getComponentName();
                IZdComponentValueParser parser = this.parserContainer.getParser(
                        childComponentName);
                Map<String, Object> submitFileBlock = parser.toSubmitFileBlock(child, item,
                        ctx);
                if (Objects.nonNull(submitFileBlock)) {
                    childrenSubmitFieldInnerValue.add(submitFileBlock);
                }
            }
            childrenSubmitFieldList.add(childrenSubmitFieldInnerValue);
        }
        submitField.put(FormSubmitFieldConstant.CHILDREN, childrenSubmitFieldList);

        return submitField;
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, Object> toSubmitFileBlockOnImport(ZdPageComponentDTO pageComponent,
                                                         Map<String, Object> valueMap, ZdImportContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);

        List<ZdPageComponentDTO> children = pageComponent.getChildren();
        List<Map<String, Object>> list = (List<Map<String, Object>>) value;
        List<List<Map<String, Object>>> childrenSubmitFieldList = new LinkedList<>();
        for (Map<String, Object> item : list) {
            List<Map<String, Object>> childrenSubmitFieldInnerValue = new LinkedList<>();
            for (ZdPageComponentDTO child : children) {
                String childComponentName = child.getComponentName();
                IZdComponentValueParser parser = this.parserContainer.getParser(
                        childComponentName);
                Map<String, Object> submitFileBlock = parser.toSubmitFileBlockOnImport(child, item,
                        ctx);
                if (Objects.nonNull(submitFileBlock)) {
                    childrenSubmitFieldInnerValue.add(submitFileBlock);
                }
            }
            childrenSubmitFieldList.add(childrenSubmitFieldInnerValue);
        }
        submitField.put(FormSubmitFieldConstant.CHILDREN, childrenSubmitFieldList);

        return submitField;
    }

    @Override
    public ZdExportDataParseResult getExportData(Map<String, Object> submitFieldMap,
                                                 ZdParseExportValueContext ctx) {
        Long id = ctx.getId();
        ZdFormDataExportContext exportContext = ctx.getCtx();
        Map<String, Integer> keyAndMaxRowMap = ctx.getKeyAndMaxRowMap();

        List<Map<String, Object>> valueList = (List<Map<String, Object>>) MapUtils.getObject(
                submitFieldMap,
                FormSubmitFieldConstant.VALUE);
        Object childrenObj = MapUtils.getObject(submitFieldMap,
                FormSubmitFieldConstant.CHILDREN);
        int maxRow = 0;
        Object storeValue = null;
        if (childrenObj instanceof List) {
            List<List<Object>> list = (List<List<Object>>) childrenObj;
            if (CollUtil.isNotEmpty(list)) {
                List<Map<String, Object>> childFormDataList = new LinkedList<>();
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> valueMap = valueList.get(i);
                    String childId = MapUtils.getString(valueMap, KEY_FIELD_NAME);
                    List<Object> childSubmitFields = list.get(i);
                    Map<String, Object> childFormData = new HashMap<>(childSubmitFields.size());
                    Integer currentMaxRow = getPageData(childSubmitFields, id,
                            childId, exportContext, childFormData, null, keyAndMaxRowMap,
                            this.parserContainer);

                    childFormData.put(KEY_FIELD_NAME, childId);

                    childFormDataList.add(childFormData);
                    if (Objects.nonNull(keyAndMaxRowMap)) {
                        keyAndMaxRowMap.put(childId, currentMaxRow);
                    }
                    maxRow += currentMaxRow;
                }
                storeValue = childFormDataList;
            }
        }
        return new ZdExportDataParseResult(storeValue, maxRow);
    }
}
