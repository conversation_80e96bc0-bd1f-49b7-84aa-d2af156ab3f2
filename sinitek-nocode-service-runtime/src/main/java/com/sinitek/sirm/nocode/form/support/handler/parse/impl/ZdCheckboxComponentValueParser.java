package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:27
 */
@Slf4j
public class ZdCheckboxComponentValueParser implements IZdComponentValueParser {

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_CHECKBOX.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
                                            Map<String, Object> convertedData,
                                            Map<String, Object> sourceMap, ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        Object value = sourceMap.get(ref);
        List<Object> valueList = this.parseZdCheckboxComponentValue(
                component, value);
        convertedData.put(ref, valueList);
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, Object> toSubmitFileBlock(ZdPageComponentDTO pageComponent,
                                                 Map<String, Object> valueMap, ZdParseContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);

        Map<String, String> valueAndLabelOptionMap = pageComponent.getValueAndLableOptionMap();
        List<String> valueList = (List<String>) value;

        List<String> valueInOption = new LinkedList<>();
        List<String> displayValueList = new LinkedList<>();

        valueList.forEach(item -> {
            String labelInOption = MapUtils.getString(valueAndLabelOptionMap, item);
            valueInOption.add(String.format("%s:%s", item, labelInOption));
            displayValueList.add(labelInOption);
        });

        submitField.put(FormSubmitFieldConstant.VALUE, valueInOption);
        submitField.put(FormSubmitFieldConstant.DISPLAY_VALUE,
                String.join(",", displayValueList));

        return submitField;
    }

    private List<Object> parseZdCheckboxComponentValue(ZdPageComponentDTO component,
                                                       Object value) {
        List<Object> valueList;
        if (Objects.isNull(value)) {
            valueList = null;
        } else {
            String ref = component.getRef();
            Map<String, Object> optionMap = component.getLabelAndValueOptionMap();
            String labelFromExcelMultiStr = (String) value;
            valueList = Arrays.stream(labelFromExcelMultiStr.split(","))
                    .map(singleLabel -> {
                        Object matchedValue = MapUtils.getObject(optionMap, singleLabel);
                        if (Objects.isNull(matchedValue)) {
                            log.warn("{} 组件,值 {} 在选项 {} 中没有找到对应的label", ref,
                                    singleLabel, JsonUtil.toJsonString(optionMap));
                            this.throwParseException(
                                    String.format("选项 %s 没有对应的选项值", singleLabel));
                        }
                        return matchedValue;
                    })
                    .collect(Collectors.toList());
        }
        return valueList;
    }
}
