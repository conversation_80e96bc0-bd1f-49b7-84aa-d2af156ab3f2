package com.sinitek.sirm.nocode.form.support.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_DATA_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_HEAD_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_IMPORT_DATA_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_PARSE_FAILED;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_SUB_FORM_MERGE_DATA_NOT_ALLOWED;

/**
 * <AUTHOR>
 * @date 2025-07-17 16:13
 */
@Slf4j
public class ImportExcelParseListener implements ReadListener<Map<Integer, Object>> {

    // 导入数据最大条数 2表头+5000条数据
    private static final int MAX_ROW_COUNT = 5002;

    // 表头行索引常量
    private static final int FIRST_HEAD_ROW_INDEX = 1;
    private static final int SECOND_HEAD_ROW_INDEX = 2;

    // 表格原始数据
    private Map<Integer, Map<Integer, Object>> tableData;

    // 合并信息
    private Map<Integer, Map<Integer, CellExtra>> cellExtraData;

    // 表头名称与ref映射
    // key: labelUK
    private Map<String, String> headNameAndRefMap;

    // 表头名称与父级ref映射
    // key: labelUK
    private Map<String, String> headNameAndParentRefMap;

    // 表头
    private Integer headRowCount;

    // 列号与ref
    private Map<Integer, String> columnIndexAndRefMap;

    // 列号与父级ref映射（只有子表单中列才会有该数据）
    private Map<Integer, String> columnIndexAndParentRefMap;

    // 父级ref与列数映射
    private Map<String, Integer> parentRefAndColumnCountMap;

    // 列号与label映射
    private Map<Integer, String> columnIndexAndLabelMap;

    // 列号与父级label映射
    private Map<Integer, String> columnIndexAndParentLabelMap;

    // 解析结果
    private List<Map<String, Object>> parseResultList;

    // 表头
    private List<ZdImportFormHeaderFieldDTO> fields;

    // ref与组件名称map
    private Map<String, ZdImportFormHeaderFieldDTO> refAndFieldMap;

    // 用户自定义列设置
    private Map<Integer, ZdImportFormHeaderFieldDTO> userColumnSettingField;

    public ImportExcelParseListener(List<ZdImportFormHeaderFieldDTO> fields,
                                    Map<Integer, ZdImportFormHeaderFieldDTO> userColumnSettingField) {
        this.tableData = new HashMap<>(MAX_ROW_COUNT);
        int fieldSize = fields.size();
        this.headNameAndRefMap = new HashMap<>(fieldSize);
        this.headNameAndParentRefMap = new HashMap<>(fieldSize);
        this.refAndFieldMap = new HashMap<>(fieldSize);
        this.cellExtraData = new HashMap<>(MAX_ROW_COUNT);
        this.fields = fields;
        this.fields.forEach(field -> {
            String label = field.getLabel();
            String parentLabel = field.getParentLabel();
            String parentRef = field.getParentRef();
            String ref = field.getRef();
            String labelUK = this.getLabelUK(label, parentLabel);
            this.headNameAndRefMap.put(labelUK, ref);
            this.headNameAndParentRefMap.put(labelUK, parentRef);
            this.refAndFieldMap.put(ref, field);
        });
        this.userColumnSettingField = userColumnSettingField;
    }


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        ReadSheetHolder readSheetHolder = context.readSheetHolder();
        Integer rowIndex = readSheetHolder.getRowIndex();

        headMap.forEach((columnIndex, cellData) -> {
            String value = cellData.getStringValue();
            this.putTableData(rowIndex, columnIndex, value);
        });
    }

    @Override
    public void invoke(Map<Integer, Object> data, AnalysisContext context) {
        ReadSheetHolder readSheetHolder = context.readSheetHolder();
        Integer rowIndex = readSheetHolder.getRowIndex();
        data.forEach(
                (columnIndex, value) -> this.putTableData(rowIndex, columnIndex, value));
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof BussinessException) {
            throw exception;
        } else {
            ReadSheetHolder readSheetHolder = context.readSheetHolder();
            if (Objects.nonNull(readSheetHolder)) {
                Integer rowIndex = readSheetHolder.getRowIndex();
                String sheetName = readSheetHolder.getSheetName();
                log.error("Excel解析失败, sheetName: {}, rowIndex: {}", sheetName, rowIndex,
                        exception);
            } else {
                log.error("Excel解析失败", exception);
            }
            throw new BussinessException(EXCEL_PARSE_FAILED);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollUtil.isEmpty(this.tableData)) {
            log.error("Excel数据为空,解析失败不再继续");
            throw new BussinessException(EXCEL_DATA_IS_EMPTY);
        }

        // 确定表头
        this.buildHead();

        // 构建数据
        int dataCount = this.getDataCount();
        log.debug("表头行数: {},共有 {} 行数据", this.headRowCount, dataCount);
        if (dataCount < 1) {
            log.error("Excel中导入数据为空,解析失败不再继续");
            throw new BussinessException(EXCEL_IMPORT_DATA_IS_EMPTY);
        }

        this.parseResultList = new LinkedList<>();

        // 子表单合并行数,每执行一行数据减1
        int mergedCount = 0;
        // 当前子表单合并行数,数据一直不变
        int maxMergedCount = 0;
        for (Entry<Integer, Map<Integer, Object>> tableDataEntry : this.tableData.entrySet()) {
            int rowIndex = tableDataEntry.getKey();
            if (rowIndex < this.headRowCount) {
                log.debug("第 {} 行为表头,跳过处理", rowIndex);
                continue;
            }
            Map<Integer, CellExtra> columnAndExtraMap = this.cellExtraData.get(rowIndex);
            if (CollUtil.isEmpty(columnAndExtraMap)
                    && mergedCount == 0
                    && this.headRowCount == 1) {
                // 当前行没有合并数据
                Map<Integer, Object> columnAndDataMap = this.tableData.get(rowIndex);
                Map<String, Object> rowData = new HashMap<>(columnAndDataMap.size());
                for (Entry<Integer, Object> entry : columnAndDataMap.entrySet()) {
                    Integer columnIndex = entry.getKey();
                    Object value = entry.getValue();
                    String ref = this.columnIndexAndRefMap.get(columnIndex);
                    if (StringUtils.isBlank(ref)) {
                        ZdImportFormHeaderFieldDTO customHeadConfig = this.userColumnSettingField.get(
                                columnIndex);
                        if (Objects.nonNull(customHeadConfig)) {
                            ref = customHeadConfig.getRef();
                        }
                        if (StringUtils.isBlank(ref)) {
                            log.warn("第 {} 行,第 {} 列,数据没有对应的ref,跳过处理", rowIndex,
                                    columnIndex);
                            continue;
                        }
                    }
                    rowData.put(ref, value);
                }
                this.parseResultList.add(rowData);
            } else {
                // 存在子表单数据
                Map<Integer, CellExtra> columnAndExtraDataMap = this.cellExtraData.get(
                        rowIndex);
                int mergedColumnIndex = 0;

                Map<Integer, Object> columnAndDataMap = this.tableData.get(rowIndex);
                int allColumnCount = columnAndDataMap.size();
                Map<String, Object> rowData;
                boolean isInMergedRow = mergedCount > 0;
                if (isInMergedRow) {
                    log.debug("第 {} 行数据为子表单第 {} 条数据", rowIndex,
                            maxMergedCount - mergedCount + 1);
                    rowData = this.parseResultList.get(this.parseResultList.size() - 1);
                } else {
                    rowData = new HashMap<>(allColumnCount);
                    if (CollUtil.isNotEmpty(columnAndExtraDataMap)) {
                        mergedColumnIndex = columnAndExtraDataMap.size();
                    }
                }

                for (Entry<Integer, Object> entry : columnAndDataMap.entrySet()) {
                    Integer columnIndex = entry.getKey();
                    Object data = entry.getValue();
                    String ref = this.columnIndexAndRefMap.get(columnIndex);

                    if (StringUtils.isBlank(ref)) {
                        ZdImportFormHeaderFieldDTO customHeadConfig = this.userColumnSettingField.get(
                                columnIndex);
                        if (Objects.nonNull(customHeadConfig)) {
                            ref = customHeadConfig.getRef();
                        }
                        if (StringUtils.isBlank(ref)) {
                            log.warn("第 {} 行,第 {} 列,数据没有对应的ref,跳过处理", rowIndex,
                                    columnIndex);
                            continue;
                        }
                    }

                    CellExtra cellExtra = null;
                    if (CollUtil.isNotEmpty(columnAndExtraDataMap)) {
                        cellExtra = columnAndExtraDataMap.get(columnIndex);
                    }
                    String parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
                    if (StringUtils.isNotBlank(parentLabel) && Objects.nonNull(cellExtra)) {
                        log.error("首行首列编号从1开始,第 {} 行,第 {} 列,子表单不允许合并数据",
                                rowIndex + 1, columnIndex + 1);
                        throw new BussinessException(EXCEL_SUB_FORM_MERGE_DATA_NOT_ALLOWED,
                                rowIndex + 1, columnIndex + 1);
                    }
                    if (Objects.nonNull(cellExtra)) {
                        // 当前列合并
                        Integer firstRowIndex = cellExtra.getFirstRowIndex();
                        Integer lastRowIndex = cellExtra.getLastRowIndex();
                        int mergeCount = lastRowIndex - firstRowIndex + 1;
                        mergedCount = Math.max(mergeCount, mergedCount);
                        maxMergedCount = mergedCount;
                        rowData.put(ref, data);
                    } else {
                        // 子表单中数据
                        String parentRef = this.columnIndexAndParentRefMap.get(columnIndex);
                        if (StringUtils.isBlank(parentRef)) {
                            if (isInMergedRow) {
                                log.debug("当前行 {},列 {} 为合并列中非第一行,跳过处理",
                                        rowIndex,
                                        columnIndex);
                                continue;
                            } else {
                                rowData.put(ref, data);
                                log.debug(
                                        "第[{}]行[{}]列为主表单数据", rowIndex, columnIndex);
                                continue;
                            }
                        }
                        List<Map<String, Object>> list = (List<Map<String, Object>>) rowData.get(
                                parentRef);
                        if (Objects.isNull(list)) {
                            // 子表单中第一列
                            list = new LinkedList<>();
                            rowData.put(parentRef, list);
                        }

                        // 子表单数据序列
                        int dataIndex = maxMergedCount - mergedCount + 1;
                        Map<String, Object> childFormItem;
                        if (list.size() != dataIndex) {
                            childFormItem = new HashMap<>(
                                    allColumnCount - mergedColumnIndex);
                            list.add(childFormItem);
                        } else {
                            childFormItem = list.get(dataIndex - 1);
                        }
                        childFormItem.put(ref, data);

                        if ((mergedCount - 1) == 0) {
                            // 当前为子表单数据最后一行,且是当前子表单最后一列
                            // 遍历子表单数据,如果全为空,则清理掉该数据
                            Integer columnCount = this.parentRefAndColumnCountMap.get(
                                    parentRef);
                            if (Objects.equals(columnCount, childFormItem.size())) {
                                List<Map<String, Object>> filtedList = list.stream()
                                        .filter(
                                                item -> item.values().stream()
                                                        .anyMatch(Objects::nonNull))
                                        .collect(Collectors.toList());
                                rowData.put(parentRef, filtedList);
                            }
                        }
                    }
                }

                mergedCount--;
                if (mergedCount == 0) {
                    // 说明当前子表单数据已全部合并，须重置最大合并数，否则会引发多条数据导入错误
                    maxMergedCount = 0;
                }
                if (!isInMergedRow) {
                    this.parseResultList.add(rowData);
                }

            }
        }

        log.debug("解析数据 {} 条", this.parseResultList.size());
    }

    public List<ZdFormFieldDTO> findExcelHeader() {
        List<ZdFormFieldDTO> list = new LinkedList<>();
        int headCount = this.columnIndexAndLabelMap.size();
        Map<String, ZdFormFieldDTO> parentRefAndFieldMap = new HashMap<>(headCount);
        // 单行表头
        for (int columnIndex = 0; columnIndex < headCount; columnIndex++) {
            String label = this.columnIndexAndLabelMap.get(columnIndex);
            String parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
            String labelUK = this.getLabelUK(label, parentLabel);
            String ref = this.headNameAndRefMap.get(labelUK);
            String parentRef = this.headNameAndParentRefMap.get(labelUK);
            if (StringUtils.isNotBlank(parentRef)) {
                ZdFormFieldDTO parentField = parentRefAndFieldMap.get(parentRef);
                if (Objects.isNull(parentField)) {
                    parentField = new ZdFormFieldDTO();
                    parentRefAndFieldMap.put(parentRef, parentField);

                    parentField.setRef(parentRef);
                    parentField.setLabel(parentLabel);
                    parentField.setChildren(new LinkedList<>());

                    list.add(parentField);
                }
                List<ZdFormFieldDTO> children = parentField.getChildren();
                children.add(ZdFormFieldDTO.builder().ref(ref).label(label).build());
            } else {
                list.add(ZdFormFieldDTO.builder().ref(ref).label(label).build());
            }
        }
        return list;
    }

    public List<ZdImportFormHeaderFieldDTO> findFields() {
        return this.fields;
    }

    public int getHeadCount() {
        return this.headRowCount;
    }

    public int getDataCount() {
        return this.tableData.size() - this.headRowCount;
    }

    public List<Map<String, Object>> findParseResultList() {
        return this.parseResultList;
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        if (Objects.equals(extra.getType(), CellExtraTypeEnum.MERGE)) {
            this.putExtraData(extra.getFirstRowIndex(), extra.getFirstColumnIndex(), extra);
        }
    }

    private void buildHead() {
        Map<Integer, Object> columnAndValueMap = this.tableData.get(0);
        int allColumnCount = columnAndValueMap.size();

        this.columnIndexAndRefMap = new HashMap<>(allColumnCount);
        this.columnIndexAndParentLabelMap = new HashMap<>(allColumnCount);
        this.columnIndexAndParentRefMap = new HashMap<>(allColumnCount);
        this.columnIndexAndLabelMap = new HashMap<>(allColumnCount);
        Map<Integer, CellExtra> headMergeData = this.cellExtraData.get(0);
        if (CollUtil.isNotEmpty(headMergeData)) {
            // 处理表头合并
            for (int columnIndex = 0; columnIndex < allColumnCount; columnIndex++) {
                // 当前列合并数据
                CellExtra currentColumnCellExtra = headMergeData.get(columnIndex);
                ZdImportFormHeaderFieldDTO customConfig = this.userColumnSettingField.get(
                        columnIndex);
                boolean hasCustomConfig = Objects.nonNull(customConfig);

                if (Objects.nonNull(currentColumnCellExtra)) {
                    Integer firstColumnIndex = currentColumnCellExtra.getFirstColumnIndex();
                    Integer lastColumnIndex = currentColumnCellExtra.getLastColumnIndex();

                    if (Objects.equals(firstColumnIndex, lastColumnIndex)) {
                        // 列合并
                        // 第一行数据
                        String label = this.getStringCellValue(0, columnIndex);
                        this.checkHead(FIRST_HEAD_ROW_INDEX, columnIndex, label);
                        String labelUK = this.getLabelUK(label, null);
                        String ref = this.headNameAndRefMap.get(labelUK);
                        this.columnIndexAndRefMap.put(columnIndex, ref);
                        this.columnIndexAndLabelMap.put(columnIndex, label);
                    } else {
                        // 行合并
                        // 子表单中的第一列
                        boolean isFirmColumnInMergedRow = Objects.equals(columnIndex,
                                firstColumnIndex);
                        if (isFirmColumnInMergedRow) {
                            String firstRowLabel = this.getStringCellValue(0,
                                    firstColumnIndex);
                            for (int i = firstColumnIndex; i <= lastColumnIndex; i++) {
                                this.columnIndexAndParentLabelMap.put(i, firstRowLabel);
                            }
                        }
                        String parentLabel;
                        String label;
                        if (hasCustomConfig) {
                            label = customConfig.getLabel();
                            parentLabel = customConfig.getParentLabel();
                        } else {
                            label = this.getStringCellValue(1, columnIndex);
                            parentLabel = this.getStringCellValue(0, columnIndex);
                            if (StringUtils.isBlank(parentLabel)) {
                                parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
                            }
                        }
                        this.checkHead(SECOND_HEAD_ROW_INDEX, columnIndex, label);
                        String labelUK = this.getLabelUK(label, parentLabel);
                        String ref;
                        if (hasCustomConfig) {
                            ref = customConfig.getRef();
                        } else {
                            ref = this.headNameAndRefMap.get(labelUK);
                        }
                        this.columnIndexAndRefMap.put(columnIndex, ref);
                        this.columnIndexAndLabelMap.put(columnIndex, label);

                        String parentRef = this.headNameAndParentRefMap.get(labelUK);
                        this.columnIndexAndParentRefMap.put(columnIndex, parentRef);
                    }
                } else {
                    // 子表单中的非第一列
                    String parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
                    if (StringUtils.isBlank(parentLabel)) {
                        // 可能是子表单下只有一列
                        parentLabel = this.getStringCellValue(0, columnIndex);
                        this.columnIndexAndParentLabelMap.put(columnIndex, parentLabel);
                    }
                    String label;
                    if (hasCustomConfig) {
                        label = customConfig.getLabel();
                    } else {
                        label = this.getStringCellValue(1, columnIndex);
                    }
                    this.checkHead(SECOND_HEAD_ROW_INDEX, columnIndex, label);
                    String labelUK = this.getLabelUK(label, parentLabel);
                    String ref;
                    if (hasCustomConfig) {
                        ref = customConfig.getRef();
                    } else {
                        ref = this.headNameAndRefMap.get(labelUK);
                    }
                    this.columnIndexAndRefMap.put(columnIndex, ref);
                    this.columnIndexAndLabelMap.put(columnIndex, label);

                    String parentRef = this.headNameAndParentRefMap.get(labelUK);
                    this.columnIndexAndParentRefMap.put(columnIndex, parentRef);
                }
            }

            // 记录每个子表单有几列
            int allChildFormSize = this.columnIndexAndParentRefMap.size();
            this.parentRefAndColumnCountMap = new HashMap<>(allChildFormSize);
            if (allChildFormSize > 0) {
                this.columnIndexAndParentRefMap.forEach((columnIndex, parentRef) -> {
                    if (StringUtils.isNotBlank(parentRef)) {
                        this.parentRefAndColumnCountMap.merge(parentRef, 1, Integer::sum);
                    }
                });
            }

            this.headRowCount = SECOND_HEAD_ROW_INDEX;
        } else {
            // 表头不合并，表头只有一行
            columnAndValueMap.forEach((columnIndex, value) -> {
                String label = null;

                ZdImportFormHeaderFieldDTO customConfig = this.userColumnSettingField.get(
                        columnIndex);
                boolean hasCustomConfig = Objects.nonNull(customConfig);
                if (hasCustomConfig) {
                    label = customConfig.getLabel();
                } else {
                    if (Objects.nonNull(value)) {
                        label = String.valueOf(value);
                    }
                }
                this.checkHead(FIRST_HEAD_ROW_INDEX, columnIndex, label);
                String labelUK = this.getLabelUK(label, null);
                String ref = null;
                if (hasCustomConfig) {
                    ref = customConfig.getRef();
                } else {
                    ref = this.headNameAndRefMap.get(labelUK);
                }
                this.columnIndexAndRefMap.put(columnIndex, ref);
                this.columnIndexAndLabelMap.put(columnIndex, label);
            });

            // 单行表头
            this.headRowCount = 1;
        }
    }

    private void checkHead(int rowIndex, int columnIndex, String value) {
        if (StringUtils.isBlank(value)) {
            log.error("表头第[{}]行,第[{}]列存在空值", rowIndex, columnIndex);
            throw new BussinessException(EXCEL_HEAD_IS_EMPTY);
        }
    }

    private void putTableData(Integer rowIndex, Integer columnIndex, Object value) {
        Map<Integer, Object> row = MapUtils.getObject(this.tableData, rowIndex);
        if (Objects.isNull(row)) {
            row = new HashMap<>();
            this.tableData.put(rowIndex, row);
        }
        row.put(columnIndex, value);
    }

    private void putExtraData(Integer rowIndex, Integer columnIndex, CellExtra value) {
        Map<Integer, CellExtra> row = MapUtils.getObject(this.cellExtraData, rowIndex);
        if (Objects.isNull(row)) {
            row = new HashMap<>();
            this.cellExtraData.put(rowIndex, row);
        }
        row.put(columnIndex, value);
    }

    private String getLabelUK(String label, String parentLabel) {
        if (StringUtils.isNotBlank(parentLabel)) {
            return String.format("%s@%s", label, parentLabel);
        }
        return label;
    }

    private String getStringCellValue(Integer rowIndex, Integer columnIndex) {
        Map<Integer, Object> row = MapUtils.getObject(this.tableData, rowIndex);
        if (Objects.isNull(row)) {
            return null;
        }
        Object value = row.get(columnIndex);
        if (Objects.isNull(value)) {
            return null;
        }
        return String.valueOf(value);
    }
}