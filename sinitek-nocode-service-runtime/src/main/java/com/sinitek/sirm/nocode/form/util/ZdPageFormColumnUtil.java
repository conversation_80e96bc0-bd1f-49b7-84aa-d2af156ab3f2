package com.sinitek.sirm.nocode.form.util;

import com.sinitek.sirm.nocode.form.constant.ZdFrameworkColumnConstant;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-07-16 15:30
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdPageFormColumnUtil {

    public static boolean isBaseColumn(String columnName) {
        return ZdFrameworkColumnConstant.ID.equals(columnName)
            || ZdFrameworkColumnConstant.VERSION.equals(columnName)
            || ZdFrameworkColumnConstant.CREATE_TIMESTAMP.equals(columnName)
            || ZdFrameworkColumnConstant.UPDATE_TIMESTAMP.equals(columnName);
    }
}
