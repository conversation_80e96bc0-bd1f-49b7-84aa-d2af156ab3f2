package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseExportValueContext;
import com.sinitek.sirm.nocode.form.support.handler.ZdExportDataParseResult;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:17
 */
@Slf4j
public class ZDAssociationComponentValueParser implements IZdComponentValueParser {

    protected IZdPageFormDataService zdPageFormDataService;

    public ZDAssociationComponentValueParser(IZdPageFormDataService zdPageFormDataService) {
        this.zdPageFormDataService = zdPageFormDataService;
    }

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_ASSOCIATION.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
                                            Map<String, Object> convertedData,
                                            Map<String, Object> sourceMap,
                                            ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        Object value = sourceMap.get(ref);
        // 这里暂时不处理,a[xx],放到 toSubmitFileBlockOnImport 中一块处理
        convertedData.put(ref, value);
    }

    @SuppressWarnings("squid:ReturnMapCheck")
    @Override
    public Map<String, Object> toSubmitFileBlock(ZdPageComponentDTO pageComponent,
                                                 Map<String, Object> valueMap, ZdParseContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();
        String formCode = pageComponent.getFormCodeInAssociationForm();
        String valueStr = MapUtils.getString(valueMap, ref);

        if (StringUtils.isNotBlank(valueStr)) {
            String[] splitStrArray = valueStr.split(",");

            List<Long> childIds = new LinkedList<>();
            // key:idStr value:childItem
            Map<String, String> idStrAndChildItemMap = new HashMap<>(splitStrArray.length);
            List<String> formDataList = new LinkedList<>();
            List<String> submitFieldsValueList = new LinkedList<>();
            List<String> submitFieldsDisplayValueList = new LinkedList<>();
            Arrays.stream(splitStrArray).forEach(item -> {
                // a[1952248784264237057] 提取中括号内数据为id,外部数据为name
                // 格式: name[id]
                int startIndex = item.indexOf("[");
                int endIndex = item.indexOf("]");
                String name = item.substring(0, startIndex);
                String id = item.substring(startIndex + 1, endIndex);

                formDataList.add(id);
                submitFieldsValueList.add(String.format("%s:%s", id, name));
                submitFieldsDisplayValueList.add(name);
                childIds.add(Long.parseLong(id));
                idStrAndChildItemMap.put(id, item);
            });

            // 校验关联子数据是否存在
            List<Long> existsIds
                    = this.zdPageFormDataService.existsId(formCode, childIds);
            if (existsIds.size() != childIds.size()) {
                String notExistsChildIds = childIds.stream()
                        .filter(item -> !existsIds.contains(item))
                        .map(String::valueOf).map(idStrAndChildItemMap::get)
                        .collect(Collectors.joining(","));
                this.throwParseException(
                        String.format("以下关联子数据不存在: %s", notExistsChildIds));
            }

            // 设置formData
            valueMap.put(ref, formDataList);

            // 设置submitField
            Map<String, Object> submitField = new HashMap<>();
            submitField.put(FormSubmitFieldConstant.REF, ref);
            submitField.put(FormSubmitFieldConstant.LABEL, label);
            submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
            submitField.put(FormSubmitFieldConstant.VALUE, submitFieldsValueList);
            submitField.put(FormSubmitFieldConstant.DISPLAY_VALUE,
                    String.join(",", submitFieldsDisplayValueList));
            return submitField;
        } else {
            // 前端定义的规则
            // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }
    }

    @Override
    public ZdExportDataParseResult getExportData(Map<String, Object> submitFieldMap,
                                                 ZdParseExportValueContext ctx) {
        List<String> associationValueList = (List<String>) MapUtils.getObject(submitFieldMap,
                FormSubmitFieldConstant.VALUE);

        Object exportValue = null;
        if (CollUtil.isNotEmpty(associationValueList)) {
            exportValue = associationValueList.stream().map(associationValueItem -> {
                String[] splitStrArr = associationValueItem.split(":");
                return String.format("%s[%s]", splitStrArr[1], splitStrArr[0]);
            }).collect(Collectors.joining(","));
        }

        return ZdExportDataParseResult.builder().value(exportValue).maxRow(1).build();
    }
}
