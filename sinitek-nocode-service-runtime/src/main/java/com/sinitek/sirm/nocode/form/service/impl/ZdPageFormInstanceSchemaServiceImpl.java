package com.sinitek.sirm.nocode.form.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.support.json.Base64Decode;
import com.sinitek.sirm.nocode.common.utils.CodeCreateUtil;
import com.sinitek.sirm.nocode.form.constant.ComponentFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCreateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaDiffComponentItemDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaUpdateParamDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sinitek.sirm.nocode.common.constant.CacheKeyConstant.FORM_INSTANCE_SCHEMA;
import static com.sinitek.sirm.nocode.common.constant.CacheKeyConstant.FORM_INSTANCE_SCHEMA_ID;
import static com.sinitek.sirm.nocode.form.constant.FormInstanceMessageCodeConstant.SCHEMA_CACHE_CREATE_FAILED;
import static com.sinitek.sirm.nocode.form.constant.FormInstanceMessageCodeConstant.SCHEMA_CACHE_UPDATE_FAILED;

/**
 * 页面实例Schema缓存服务实现
 *
 * <AUTHOR>
 * @date 2025-08-15 13:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZdPageFormInstanceSchemaServiceImpl implements IZdPageFormInstanceSchemaService {

    private static final String SCHEMA_ID_PREFIX = "schema_";

    private static final String COMPONENT_TYPE = "component";

    // 分布式锁相关常量
    private static final String SCHEMA_ID_LIST_LOCK_KEY = "schema_id_list_lock";
    private static final String SCHEMA_UPDATE_LOCK_PREFIX = "schema_update_lock_";
    private static final long LOCK_WAIT_TIME = 3L; // 等待锁的时间（秒）
    private static final long LOCK_LEASE_TIME = 10L; // 锁的持有时间（秒）

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 创建页面实例Schema缓存
     *
     * @param param 创建参数
     * @return schemaId
     */
    @Override
    public String createSchemaCache(ZdFormInstanceSchemaCreateParamDTO param) {

        log.debug("开始创建页面实例Schema缓存");

        try {
            // 生成唯一的schemaId
            String schemaId = CodeCreateUtil.createCode(SCHEMA_ID_PREFIX);

            // 解码Base64编码的Schema数据
            String decodedSchema = Base64Decode.decode(param.getSchema());

            // 解析Schema为Map对象
            Map<String, Object> schemaMap = JsonUtil.toMap(decodedSchema);

            // 创建缓存DTO并存储到缓存
            ZdFormInstanceSchemaCacheDTO cacheDTO = new ZdFormInstanceSchemaCacheDTO();
            cacheDTO.setSchemaId(schemaId);
            cacheDTO.setSchema(schemaMap);
            cacheDTO.setCreateTime(new Date());
            cacheDTO.setUpdateTime(new Date());

            Cache cache = this.getCache();
            cache.put(schemaId, cacheDTO);

            this.addSchemaId(schemaId);

            log.debug("成功创建页面实例Schema缓存，schemaId: {}", schemaId);
            return schemaId;
        } catch (Exception e) {
            log.error("创建页面实例Schema缓存失败:{}", e.getMessage(), e);
            throw new BussinessException(SCHEMA_CACHE_CREATE_FAILED);
        }
    }

    private Cache getCache() {
        Cache cache = this.cacheManager.getCache(FORM_INSTANCE_SCHEMA);
        if (Objects.isNull(cache)) {
            throw new BussinessException("缓存未初始化");
        }
        return cache;
    }

    private Cache getIdCache() {
        Cache cache = this.cacheManager.getCache(FORM_INSTANCE_SCHEMA_ID);
        if (Objects.isNull(cache)) {
            throw new BussinessException("缓存未初始化");
        }
        return cache;
    }

    private static final String SCHEMA_ID_LIST = "schema_id_list";

    private void addSchemaId(String id) {
        RLock lock = redissonClient.getLock(SCHEMA_ID_LIST_LOCK_KEY);
        try {
            boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (locked) {
                try {
                    Cache cache = this.getIdCache();
                    List<String> list = (List<String>) cache.get(SCHEMA_ID_LIST, List.class);
                    if (Objects.isNull(list) || CollUtil.isEmpty(list)) {
                        list = new LinkedList<>();
                    }
                    list.add(id);
                    cache.put(SCHEMA_ID_LIST, list);
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取Schema ID列表锁失败，添加操作跳过，schemaId: {}", id);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("添加Schema ID时获取锁被中断，schemaId: {}", id, e);
        }
    }

    private void removeSchemaId(String id) {
        RLock lock = redissonClient.getLock(SCHEMA_ID_LIST_LOCK_KEY);
        try {
            boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (locked) {
                try {
                    Cache cache = this.getIdCache();
                    List<String> list = (List<String>) cache.get(SCHEMA_ID_LIST, List.class);
                    if (Objects.isNull(null) || CollUtil.isEmpty(list)) {
                        return;
                    }
                    List<String> newList = list.stream().filter(v -> !Objects.equals(v, id))
                            .collect(Collectors.toList());
                    cache.put(SCHEMA_ID_LIST, newList);
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取Schema ID列表锁失败，删除操作跳过，schemaId: {}", id);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("删除Schema ID时获取锁被中断，schemaId: {}", id, e);
        }
    }

    private void removeSchemaIdBatch(Collection<String> ids) {
        RLock lock = redissonClient.getLock(SCHEMA_ID_LIST_LOCK_KEY);
        try {
            boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (locked) {
                try {
                    Cache cache = this.getIdCache();
                    List<String> list = (List<String>) cache.get(SCHEMA_ID_LIST, List.class);
                    if (Objects.isNull(list) || CollUtil.isEmpty(list)) {
                        return;
                    }
                    Map<String, String> idMap = ids.stream().collect(Collectors.toMap(v -> v, v -> v));
                    List<String> newList = list.stream().filter(v -> !idMap.containsKey(v))
                            .collect(Collectors.toList());
                    cache.put(SCHEMA_ID_LIST, newList);
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取Schema ID列表锁失败，批量删除操作跳过，schemaIds: {}", ids);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("批量删除Schema ID时获取锁被中断，schemaIds: {}", ids, e);
        }
    }

    /**
     * 删除页面实例Schema缓存
     *
     * @param schemaId Schema ID
     */
    @Override
    public void deleteSchemaCache(String schemaId) {
        if (StringUtils.isBlank(schemaId)) {
            log.warn("删除Schema缓存失败：schemaId为空");
            return;
        }
        Cache cache = this.getCache();
        cache.evict(schemaId);
        this.removeSchemaId(schemaId);
        log.debug("成功删除页面实例Schema缓存，schemaId: {}", schemaId);
    }

    @Override
    public void deleteSchemaCacheBatch(Collection<String> schemaIds) {
        if (CollUtil.isEmpty(schemaIds)) {
            return;
        }
        Cache cache = this.getCache();

        schemaIds.forEach(cache::evict);
        this.removeSchemaIdBatch(schemaIds);

        if (log.isDebugEnabled()) {
            log.debug("成功删除页面实例Schema缓存，schemaId: {}", schemaIds);
        }
    }

    /**
     * 更新页面实例Schema缓存
     * <p>
     * 使用分布式锁保证并发安全
     *
     * @param param 更新参数
     */
    @Override
    public void updateSchemaCache(ZdFormInstanceSchemaUpdateParamDTO param) {
        String schemaId = param.getSchemaId();
        log.debug("开始更新页面实例Schema缓存，schemaId: {}", schemaId);

        String lockKey = SCHEMA_UPDATE_LOCK_PREFIX + schemaId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (locked) {
                try {
                    ZdFormInstanceSchemaCacheDTO existingCache = getSchemaCache(schemaId);
                    if (Objects.isNull(existingCache)) {
                        log.warn("更新Schema缓存失败：找不到对应的Schema，schemaId: {}", schemaId);
                        return;
                    }

                    // 解码Base64编码的diff数据
                    String decodedDiff = Base64Decode.decode(param.getDiff());

                    // 解析diff数据为组件变更列表
                    List<ZdFormInstanceSchemaDiffComponentItemDTO> diffList = JsonUtil.toJavaObjectList(
                            decodedDiff, ZdFormInstanceSchemaDiffComponentItemDTO.class);

                    // 应用diff变更到现有Schema
                    Map<String, Object> schema = existingCache.getSchema();
                    Map<String, Object> updatedSchema = this.applyDiffToSchema(schema, diffList);

                    // 更新缓存
                    existingCache.setSchema(updatedSchema);
                    existingCache.setUpdateTime(new Date());
                    Cache cache = this.getCache();
                    cache.put(schemaId, existingCache);

                    log.debug("成功更新页面实例Schema缓存，schemaId: {}", schemaId);
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取Schema更新锁失败，更新操作跳过，schemaId: {}", schemaId);
                throw new BussinessException(SCHEMA_CACHE_UPDATE_FAILED);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("更新Schema缓存时获取锁被中断，schemaId: {}", schemaId, e);
            throw new BussinessException(SCHEMA_CACHE_UPDATE_FAILED);
        } catch (Exception e) {
            log.error("更新页面实例Schema缓存失败，schemaId: {}", schemaId, e);
            throw new BussinessException(SCHEMA_CACHE_UPDATE_FAILED);
        }
    }

    /**
     * 获取页面实例Schema缓存
     *
     * @param schemaId Schema ID
     * @return Schema缓存DTO
     */
    @Override
    public ZdFormInstanceSchemaCacheDTO getSchemaCache(String schemaId) {
        if (StringUtils.isBlank(schemaId)) {
            log.warn("获取Schema缓存失败：schemaId为空");
            return null;
        }
        Cache cache = this.getCache();
        ZdFormInstanceSchemaCacheDTO result = cache.get(schemaId,
                ZdFormInstanceSchemaCacheDTO.class);
        log.debug("从缓存中获取Schema，schemaId: {}", schemaId);
        return result;
    }

    @Override
    public List<String> findCachedSchemaId() {
        Cache cache = this.getIdCache();
        List<String> list = (List<String>) cache.get(SCHEMA_ID_LIST, List.class);
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return Collections.emptyList();
    }

    /**
     * 应用diff变更到Schema
     *
     * @param originalSchema 原始Schema
     * @param diffList       diff变更列表
     * @return 更新后的Schema
     */
    @SuppressWarnings({"squid:ReturnMapCheck", "unchecked"})
    private Map<String, Object> applyDiffToSchema(Map<String, Object> originalSchema,
                                                  List<ZdFormInstanceSchemaDiffComponentItemDTO> diffList) {
        log.debug("开始应用diff变更到Schema，变更数量: {}", diffList.size());

        // 深拷贝原始Schema，避免修改原对象
        Map<String, Object> updatedSchema = JsonUtil.jsonCopy(originalSchema, Map.class);

        Map<String, Map<String, Object>> refAndComponentMap = new HashMap<>(diffList.size());

        for (ZdFormInstanceSchemaDiffComponentItemDTO diffItem : diffList) {
            String diffItemType = diffItem.getType();
            if (COMPONENT_TYPE.equals(diffItemType)) {
                Map<String, Object> data = diffItem.getData();
                String componentRef = MapUtils.getString(data, ComponentFieldConstant.REF);
                refAndComponentMap.put(componentRef, data);
            } else {
                log.warn("暂不支持 {} 类型的diff", diffItemType);
            }
        }

        this.applyComponentDiff(updatedSchema, refAndComponentMap);

        log.debug("完成diff变更应用");
        return updatedSchema;
    }

    /**
     * 应用组件diff变更
     *
     * @param schema             Schema对象
     * @param refAndComponentMap 组件数据
     */
    private void applyComponentDiff(Map<String, Object> schema,
                                    Map<String, Map<String, Object>> refAndComponentMap) {

        List<Map<String, Object>> children = (List<Map<String, Object>>) schema.get(
                ComponentFieldConstant.CHILDREN);

        for (Map<String, Object> child : children) {
            if (CollUtil.isEmpty(refAndComponentMap)) {
                break;
            }
            String childRef = MapUtils.getString(child, ComponentFieldConstant.REF);
            Map<String, Object> data = refAndComponentMap.remove(childRef);
            if (CollUtil.isNotEmpty(data)) {
                // 找到匹配的组件，更新数据
                this.updateComponentInList(child, data);
            }
            if (CollUtil.isEmpty(refAndComponentMap)) {
                break;
            }
            Object childrenInChild = child.get(ComponentFieldConstant.CHILDREN);
            if (childrenInChild instanceof List) {
                this.applyComponentDiff(child, refAndComponentMap);
            }
        }
    }

    /**
     * 在组件列表中更新组件数据
     *
     * @param component     组件
     * @param diffComponent 组件diff数据
     */
    private void updateComponentInList(Map<String, Object> component,
                                       Map<String, Object> diffComponent) {
        // 更新组件属性（全量替换或部分更新）
        diffComponent.forEach((key, value) -> {
            if (Objects.equals(key, ComponentFieldConstant.PROPS)) {
                // props 部分更新
                Map<String, Object> propsInDiff = (Map<String, Object>) value;
                if (CollUtil.isNotEmpty(propsInDiff)) {
                    Map<String, Object> componentProps = (Map<String, Object>) component.get(
                            ComponentFieldConstant.PROPS);
                    componentProps.putAll(propsInDiff);
                }
            } else {
                // 其他属性全量更新
                component.put(key, value);
            }
        });
    }
}
