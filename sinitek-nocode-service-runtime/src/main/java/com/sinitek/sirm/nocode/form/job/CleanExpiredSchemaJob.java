package com.sinitek.sirm.nocode.form.job;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 清理过期Schema缓存定时任务
 * <p>
 * 根据设计文档要求，页面关闭时需通知后端卸载实例Schema，
 * 如果没有通知，则3小时后自动卸载
 *
 * <AUTHOR>
 * @date 2025-08-15 15:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CleanExpiredSchemaJob {

    private final IZdPageFormInstanceSchemaService service;

    private static final long CACHE_EXPIRE_TIME_MS = 10800000L;

    /**
     * 定时清理过期的Schema缓存
     * 每3小时执行一次
     */
    @Scheduled(fixedRate = 10800000L)
    public void cleanExpiredSchemaCache() {
        log.info("开始执行Schema缓存清理任务");

        try {
            List<String> cachedSchemaIds = this.service.findCachedSchemaId();
            long currentTimeMillis = System.currentTimeMillis();
            List<String> needRemoveSchemaIds;
            if (CollUtil.isNotEmpty(cachedSchemaIds)) {
                needRemoveSchemaIds = cachedSchemaIds.stream().map(cachedSchemaId -> {
                    ZdFormInstanceSchemaCacheDTO data = this.service.getSchemaCache(
                            cachedSchemaId);
                    if (this.isExpired(data, currentTimeMillis)) {
                        return cachedSchemaId;
                    } else {
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
                this.service.deleteSchemaCacheBatch(needRemoveSchemaIds);
            } else {
                needRemoveSchemaIds = Collections.emptyList();
            }
            log.info("Schema缓存清理任务执行完成，清理数量: {}", needRemoveSchemaIds.size());
        } catch (Exception e) {
            log.error("Schema缓存清理任务执行失败:{}", e.getMessage(), e);
        }
    }

    /**
     * 检查Schema缓存是否过期
     *
     * @param cacheDTO    缓存DTO
     * @param currentTime 当前时间
     * @return 是否过期
     */
    private boolean isExpired(ZdFormInstanceSchemaCacheDTO cacheDTO, long currentTime) {
        if (Objects.isNull(cacheDTO) || Objects.isNull(cacheDTO.getUpdateTime())) {
            return true;
        }

        long updateTime = cacheDTO.getUpdateTime().getTime();
        return (currentTime - updateTime) >= CACHE_EXPIRE_TIME_MS;
    }
}
