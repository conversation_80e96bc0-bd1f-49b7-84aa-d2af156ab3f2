package com.sinitek.sirm.nocode.form.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.app.dto.ZdImportPreParseParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportPreParseResultDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskCreateParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskDeleteAddedParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskRetryParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.form.aspect.ZdAuth;
import com.sinitek.sirm.nocode.form.dto.ZdDownloadImportTemplateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportOrImportLoadDetailParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdGenerateImportExcelTemplateResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataErrorMsgDTO;
import com.sinitek.sirm.nocode.form.formatter.ZdImportTaskSearchResultFormatter;
import com.sinitek.sirm.nocode.form.service.IZdImportDataService;
import com.sinitek.sirm.nocode.form.service.IZdImportTaskService;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 2025.07.03
 * @description 数据导出
 * @since 1.0.0
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/import-task", tags = "智搭-数据导入")
@RequestMapping("/frontend/api/nocode/import-task")
public class ZdImportTaskController {

    @Autowired
    private IZdImportDataService importDataService;

    @Autowired
    private IZdImportTaskService importTaskService;

    @Autowired
    private ZdImportTaskSearchResultFormatter formatter;

    /**
     * 查询导入任务列表
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "查询导入任务列表")
    @PostMapping(path = "/search")
    public TableResult<ZdImportTaskSearchResultDTO> search(
            @RequestBody @Valid ZdImportTaskSearchParamDTO param) {
        IPage<ZdImportTaskSearchResultDTO> search = this.importTaskService.search(param);
        return param.build(search, this.formatter);
    }

    @ApiOperation(value = "预解析导入文件")
    @PostMapping(path = "/pre-parse")
    public RequestResult<ZdImportPreParseResultDTO> preParse(
            @RequestBody @Valid ZdImportPreParseParamDTO param) throws Exception {
        param.setOperatorId(CurrentUserFactory.getCurrentUserInfo().getOrgId());
        param.setOperateTime(new Date());
        log.info("操作人 {} 预解析导入文件，表单编码: {}", param.getOperatorId(), param.getFormCode());
        ZdImportPreParseResultDTO result = this.importTaskService.preParse(param);
        return new RequestResult<>(result);
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.EDIT)
    @ApiOperation(value = "导入")
    @PostMapping(path = "/import")
    public RequestResult<Void> importExcel(
            @RequestBody @Valid ZdImportTaskCreateParamDTO param) throws Exception {
        param.setOperatorId(CurrentUserFactory.getCurrentUserInfo().getOrgId());
        param.setOperateTime(new Date());
        this.importDataService.importData(param);
        return new RequestResult<>();
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.EDIT)
    @ApiOperation(value = "重试")
    @PostMapping(path = "/retry")
    public RequestResult<Void> retryImportExcel(
            @RequestBody @Valid ZdImportTaskRetryParamDTO param) throws Exception {
        param.setOperatorId(CurrentUserFactory.getCurrentUserInfo().getOrgId());
        param.setOperateTime(new Date());

        log.info("操作人 {} 重试导入任务，导入任务ID: {}", param.getOperatorId(), param.getTaskId());

        this.importDataService.retry(param);
        return new RequestResult<>();
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.DELETE)
    @ApiOperation(value = "删除新增数据")
    @PostMapping(path = "/delete-added-import-data")
    public RequestResult<Void> deleteAddedImportData(
            @RequestBody @Valid ZdImportTaskDeleteAddedParamDTO param) throws Exception {
        param.setOperatorId(CurrentUserFactory.getCurrentUserInfo().getOrgId());
        param.setOperateTime(new Date());

        log.info("操作人 {} 删除导入任务新增数据，导入任务ID: {}", param.getOperatorId(),
                param.getTaskId());

        this.importDataService.deleteAddedImportData(param);
        return new RequestResult<>();
    }

    @ApiOperation(value = "生成导入模板")
    @RequestMapping(path = "/generate-import-template")
    public void generateImportTemplate(HttpServletResponse response,
                                       @ModelAttribute @Valid ZdDownloadImportTemplateParamDTO param) throws Exception {
        ZdGenerateImportExcelTemplateResultDTO result =
                this.importTaskService.generateImportExcelTemplate(param);
        HttpUtils.download(response, result.getFile(),
                String.format("%s_数据模板.xlsx", result.getFormName()));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "加载导入文件id")
    @PostMapping("/attachment/load-resource-attachment-id")
    public RequestResult<String> loadResourceAttachmentId(
            @RequestBody @ApiParam(value = "加载Excel文件id参数", name = "加载Excel文件id参数") @Validated(
                    value = ZdIdDTO.View.class) ZdExportOrImportLoadDetailParamDTO param) {
        return new RequestResult<>(this.importTaskService.loadImportResourceAttachmentId(param));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "加载导入失败文件id")
    @PostMapping("/attachment/load-failure-attachment-id")
    public RequestResult<String> loadFailureAttachmentId(
            @RequestBody @ApiParam(value = "加载Excel文件id参数", name = "加载Excel文件id参数") @Validated(
                    value = ZdIdDTO.View.class) ZdExportOrImportLoadDetailParamDTO param) {
        return new RequestResult<>(this.importTaskService.loadImportFailureAttachmentId(param));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "查看失败原因")
    @PostMapping("/load-error-detail")
    public RequestResult<ZdImportDataErrorMsgDTO> loadErrorDetail(
            @RequestBody @ApiParam(value = "查看失败原因", name = "查看失败原因") @Validated(
                    value = ZdIdDTO.View.class) ZdExportOrImportLoadDetailParamDTO param) {
        Long taskId = param.getTaskId();
        return new RequestResult<>(this.importTaskService.getImportTaskErrorDetail(taskId));
    }
}
