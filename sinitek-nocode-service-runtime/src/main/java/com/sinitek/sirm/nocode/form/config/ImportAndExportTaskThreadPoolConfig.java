package com.sinitek.sirm.nocode.form.config;

import com.sinitek.sirm.nocode.common.support.thread.ContextThreadCleaner;
import com.sinitek.sirm.nocode.form.constant.ExportAndImportTaskExecutorConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 导出任务线程池配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Configuration
public class ImportAndExportTaskThreadPoolConfig {
    // 线程池配置常量
    private static final int CORE_POOL_SIZE = 5;
    private static final int MAX_POOL_SIZE = 20;
    private static final int QUEUE_CAPACITY = 100;
    private static final int KEEP_ALIVE_SECONDS = 60;

    /**
     * 导入任务线程池
     */
    @Bean(ExportAndImportTaskExecutorConstant.DEFAULT_IMPORT_TASK_EXECUTOR_NAME)
    public Executor importTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);

        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);

        // 队列容量
        executor.setQueueCapacity(QUEUE_CAPACITY);

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);

        // 线程名前缀
        executor.setThreadNamePrefix("import-task-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(KEEP_ALIVE_SECONDS);

        // 自定义线程工厂，添加线程上下文清理
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } finally {
                // 清理线程上下文
                ContextThreadCleaner.cleanAll();
            }
        });

        executor.initialize();

        log.info("导入任务线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }

    /**
     * 导出任务线程池
     */
    @Bean(ExportAndImportTaskExecutorConstant.DEFAULT_EXPORT_TASK_EXECUTOR_NAME)
    public Executor exportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);

        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);

        // 队列容量
        executor.setQueueCapacity(QUEUE_CAPACITY);

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);

        // 线程名前缀
        executor.setThreadNamePrefix("export-task-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(KEEP_ALIVE_SECONDS);

        // 自定义线程工厂，添加线程上下文清理
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } finally {
                // 清理线程上下文
                ContextThreadCleaner.cleanAll();
            }
        });

        executor.initialize();

        log.info("导出任务线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }

    /**
     * 导出任务资源文件线程池
     */
    @Bean(ExportAndImportTaskExecutorConstant.DEFAULT_RESOURCE_EXPORT_TASK_EXECUTOR_NAME)
    public Executor exportResourceTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);

        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);

        // 队列容量
        executor.setQueueCapacity(QUEUE_CAPACITY);

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);

        // 线程名前缀
        executor.setThreadNamePrefix("export-resource-task-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(KEEP_ALIVE_SECONDS);

        // 自定义线程工厂，添加线程上下文清理
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } finally {
                // 清理线程上下文
                ContextThreadCleaner.cleanAll();
            }
        });

        executor.initialize();

        log.info("导出任务导出资源线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }

    /**
     * 文件下载线程池
     */
    @Bean(ExportAndImportTaskExecutorConstant.DEFAULT_DOWNLOAD_RESOURCE_EXECUTOR_NAME)
    public Executor downloadResourceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);

        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);

        // 队列容量
        executor.setQueueCapacity(QUEUE_CAPACITY);

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);

        // 线程名前缀
        executor.setThreadNamePrefix("download-resource-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(KEEP_ALIVE_SECONDS);

        // 自定义线程工厂，添加线程上下文清理
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } finally {
                // 清理线程上下文
                ContextThreadCleaner.cleanAll();
            }
        });

        executor.initialize();

        log.info("文件下载线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }
}